package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * DeepSeek API 服务 - 对话功能
 */
@Service
@Slf4j
class DeepSeekApiService {

    @Value('${deepseek.chat.api-key}')
    private String apiKey

    @Value('${deepseek.chat.api-url}')
    private String apiUrl

    @Value('${deepseek.chat.model}')
    private String model

    @Value('${deepseek.chat.timeout:15.0}')
    private Double timeout

    @Value('${deepseek.chat.max-tokens:4000}')
    private Integer maxTokens

    @Value('${deepseek.chat.temperature:0.3}')
    private Double temperature

    private RestTemplate restTemplate = new RestTemplate()

    /**
     * 单轮对话
     */
    Map chat(String userMessage) {
        List<Map> messages = [
            [role: "system", content: "你是一个友善、有帮助的AI助手。请用简洁明了的方式回答用户的问题。"],
            [role: "user", content: userMessage]
        ]
        
        return chatWithMessages(messages)
    }

    /**
     * 带上下文的多轮对话
     */
    Map chatWithContext(List<Map> conversationHistory, String userMessage) {
        List<Map> messages = []
        
        // 添加系统提示
        messages.add([role: "system", content: "你是一个友善、有帮助的AI助手。请用简洁明了的方式回答用户的问题。"])
        
        // 添加对话历史
        messages.addAll(conversationHistory)
        
        // 添加当前用户消息
        messages.add([role: "user", content: userMessage])
        
        return chatWithMessages(messages)
    }

    /**
     * 发送消息到 DeepSeek API
     */
    private Map chatWithMessages(List<Map> messages) {
        try {
            log.info("发送对话请求到 DeepSeek API, 消息数量: {}", messages.size())

            // 构建请求体
            Map requestBody = [
                model: model,
                messages: messages,
                max_tokens: maxTokens,
                temperature: temperature,
                stream: false
            ]

            // 构建请求头
            HttpHeaders headers = createHeaders()
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)

            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                apiUrl,
                HttpMethod.POST,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("DeepSeek API 响应成功")

            // 提取回复内容
            String reply = extractReplyContent(result)
            
            return [
                success: true,
                reply: reply,
                usage: result?.usage,
                model: result?.model
            ]

        } catch (Exception e) {
            log.error("DeepSeek API 调用失败", e)
            return [
                success: false,
                error: "AI对话失败: ${e.message}"
            ]
        }
    }

    /**
     * 从响应中提取回复内容
     */
    private String extractReplyContent(Map response) {
        try {
            List choices = response?.choices
            if (choices && !choices.isEmpty()) {
                Map firstChoice = choices[0] as Map
                Map message = firstChoice?.message as Map
                return message?.content?.toString()?.trim() ?: "抱歉，我没有理解您的问题。"
            }
            return "抱歉，我没有收到有效的回复。"
        } catch (Exception e) {
            log.warn("提取回复内容失败", e)
            return "抱歉，处理回复时出现了问题。"
        }
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders()
        headers.set("Authorization", "Bearer ${apiKey}")
        headers.set("Content-Type", "application/json")
        return headers
    }

    /**
     * 检查 API 配置是否有效
     */
    boolean isConfigValid() {
        return apiKey && apiUrl && model && 
               !apiKey.isEmpty() && !apiUrl.isEmpty() && !model.isEmpty()
    }

    /**
     * 获取配置信息（用于调试）
     */
    Map getConfigInfo() {
        return [
            model: model,
            maxTokens: maxTokens,
            temperature: temperature,
            timeout: timeout,
            apiConfigured: isConfigValid()
        ]
    }
}
