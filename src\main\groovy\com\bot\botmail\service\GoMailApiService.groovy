package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

import java.security.SecureRandom

/**
 * GoMail 临时邮箱 API 服务
 */
@Service
@Slf4j
class GoMailApiService {

    @Value('${gomail.api.base-url}')
    private String baseUrl

    @Value('${gomail.api.token}')
    private String apiToken

    private RestTemplate restTemplate = new RestTemplate()
    private SecureRandom random = new SecureRandom()

    // 可用的邮箱域名列表
    private static final List<String> EMAIL_DOMAINS = [
        "187qhuisnj1.dpdns.org",
        "aisnjd652.dpdns.org",
        "gogogogoegomail.dpdns.org",
        "najb28zd.dpdns.org",
        "tghbjn621.dpdns.org",
        "xnnaish123em.dpdns.org"
    ]

    /**
     * 创建临时邮箱（随机选择域名）
     */
    Map createMailbox(String domain = null) {
        try {
            // 如果没有指定域名，随机选择一个
            if (!domain) {
                domain = getRandomDomain()
            }

            // 生成随机前缀
            String prefix = generateRandomPrefix()

            log.info("创建临时邮箱: prefix={}, domain={}", prefix, domain)

            // 构建请求体
            Map requestBody = [
                prefix: prefix,
                domain: domain
            ]

            // 构建请求头
            HttpHeaders headers = createHeaders()
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)

            // 发送请求
            String url = "${baseUrl}/api/external/mailbox"
            ResponseEntity<Map> response = restTemplate.exchange(
                url, 
                HttpMethod.POST, 
                request, 
                Map.class
            )

            Map result = response.getBody()
            log.info("创建邮箱响应: {}", result)

            return result
        } catch (Exception e) {
            log.error("创建临时邮箱失败", e)
            return [
                success: false,
                error: "创建邮箱失败: ${e.message}"
            ]
        }
    }

    /**
     * 创建私人邮箱（仅限机器人主人使用）
     */
    Map createPrivateMailbox() {
        try {
            String privateDomain = "159email.shop"
            String prefix = generateRandomPrefix()

            log.info("创建私人邮箱: prefix={}, domain={}", prefix, privateDomain)

            // 构建请求体
            Map requestBody = [
                prefix: prefix,
                domain: privateDomain
            ]

            // 构建请求头
            HttpHeaders headers = createHeaders()
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)

            // 发送请求
            String url = "${baseUrl}/api/external/mailbox"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("创建私人邮箱响应: {}", result)

            return result
        } catch (Exception e) {
            log.error("创建私人邮箱失败", e)
            return [
                success: false,
                error: "创建私人邮箱失败: ${e.message}"
            ]
        }
    }

    /**
     * 创建自定义邮箱（指定前缀和域名）
     */
    Map createCustomMailbox(String prefix, String domain) {
        try {
            log.info("创建自定义邮箱: prefix={}, domain={}", prefix, domain)

            // 构建请求体
            Map requestBody = [
                prefix: prefix,
                domain: domain
            ]

            // 构建请求头
            HttpHeaders headers = createHeaders()
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)

            // 发送请求
            String url = "${baseUrl}/api/external/mailbox"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.POST,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("创建自定义邮箱响应: {}", result)

            return result
        } catch (Exception e) {
            log.error("创建自定义邮箱失败", e)
            return [
                success: false,
                error: "创建自定义邮箱失败: ${e.message}"
            ]
        }
    }



    /**
     * 获取邮件列表
     */
    Map getEmails(String email) {
        try {
            log.info("获取邮件列表: email={}", email)

            HttpHeaders headers = createHeaders()
            HttpEntity request = new HttpEntity<>(headers)

            String url = "${baseUrl}/api/external/emails/${email}"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("获取邮件列表响应: 邮件数量={}", result?.data?.totalCount ?: 0)

            return result
        } catch (Exception e) {
            log.error("获取邮件列表失败: email={}", email, e)
            return [
                success: false,
                error: "获取邮件失败: ${e.message}"
            ]
        }
    }

    /**
     * 获取邮件详情
     */
    Map getEmailDetail(String emailId) {
        try {
            log.info("获取邮件详情: emailId={}", emailId)

            HttpHeaders headers = createHeaders()
            HttpEntity request = new HttpEntity<>(headers)

            String url = "${baseUrl}/api/external/email/${emailId}"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("获取邮件详情响应: subject={}", result?.data?.subject)

            return result
        } catch (Exception e) {
            log.error("获取邮件详情失败: emailId={}", emailId, e)
            return [
                success: false,
                error: "获取邮件详情失败: ${e.message}"
            ]
        }
    }

    /**
     * 获取Token信息
     */
    Map getTokenInfo() {
        try {
            log.info("获取Token信息")

            HttpHeaders headers = createHeaders()
            HttpEntity request = new HttpEntity<>(headers)

            String url = "${baseUrl}/api/external/mailbox"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("Token信息: 剩余使用次数={}", result?.data?.remainingUsage)

            return result
        } catch (Exception e) {
            log.error("获取Token信息失败", e)
            return [
                success: false,
                error: "获取Token信息失败: ${e.message}"
            ]
        }
    }

    /**
     * 随机选择一个邮箱域名
     */
    private String getRandomDomain() {
        return EMAIL_DOMAINS[random.nextInt(EMAIL_DOMAINS.size())]
    }

    /**
     * 生成随机前缀（格式：word-word-number）
     */
    private String generateRandomPrefix() {
        List<String> adjectives = [
            "happy", "bright", "clever", "gentle", "brave", "calm", "eager", "fair", "kind", "wise",
            "bold", "cool", "fast", "free", "good", "great", "huge", "keen", "light", "nice",
            "quick", "safe", "smart", "strong", "sweet", "warm", "wild", "young", "fresh", "lucky",
            "hopeful", "peaceful", "cheerful", "graceful", "powerful", "magical", "golden", "silver",
            "crystal", "diamond", "royal", "noble", "ancient", "modern", "cosmic", "stellar"
        ]

        List<String> nouns = [
            "cat", "dog", "bird", "fish", "lion", "bear", "wolf", "fox", "deer", "eagle",
            "star", "moon", "sun", "sky", "sea", "river", "tree", "flower", "stone", "wind",
            "fire", "water", "earth", "cloud", "rain", "snow", "storm", "light", "shadow", "dream",
            "gould", "smith", "jones", "brown", "davis", "miller", "wilson", "moore", "taylor", "anderson",
            "phoenix", "dragon", "tiger", "panda", "rabbit", "turtle", "whale", "shark", "dolphin", "butterfly"
        ]

        String adjective = adjectives[random.nextInt(adjectives.size())]
        String noun = nouns[random.nextInt(nouns.size())]
        String number = String.valueOf(1000 + random.nextInt(9000)) // 4位数字 1000-9999

        return "${adjective}-${noun}-${number}"
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders()
        headers.set("Authorization", "Bearer ${apiToken}")
        headers.set("Content-Type", "application/json")
        return headers
    }
}
