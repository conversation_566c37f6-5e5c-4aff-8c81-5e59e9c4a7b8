package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.DogApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 舔狗日记 事件处理器
 */
@Component
@Slf4j
class DogHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private DogApiService dogService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理命令：我要当舔狗
        return messageText == "我要当舔狗"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        
        log.info("收到舔狗日记请求: userId={}", userId)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleDogRequest(event)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理舔狗日记请求
     */
    private void handleDogRequest(QQEvent event) {
        try {
            sendReply(event, "🐕 正在为你准备舔狗日记...")

            // 调用API获取舔狗日记
            Map result = dogService.getDogDiary()
            
            if (result.success) {
                String formattedDiary = dogService.formatDogDiary(result.diary)
                sendReply(event, formattedDiary)
                
                log.info("舔狗日记发送成功: userId={}", event.getUserIdStr())
            } else {
                sendReply(event, "❌ 获取舔狗日记失败: ${result.error}")
                log.error("获取舔狗日记失败: {}", result.error)
            }

        } catch (Exception e) {
            log.error("处理舔狗日记请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
