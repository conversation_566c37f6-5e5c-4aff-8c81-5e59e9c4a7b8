package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service

import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.LocalDate

/**
 * 过期功能清理服务 - 自动删除过期功能
 */
@Service
@Slf4j
class ExpiredFeatureCleanupService {

    // 过期日期：2025年7月5日
    private static final LocalDate EXPIRY_DATE = LocalDate.of(2025, 7, 5)

    /**
     * 每天检查一次是否需要清理过期功能
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    void checkAndCleanupExpiredFeatures() {
        try {
            LocalDate today = LocalDate.now()
            
            log.info("检查过期功能清理: 当前日期={}, 过期日期={}", today, EXPIRY_DATE)
            
            if (today.isAfter(EXPIRY_DATE)) {
                log.info("过期邮箱功能已到期，开始清理...")
                cleanupExpiredEmailFeature()
            } else {
                long daysLeft = java.time.temporal.ChronoUnit.DAYS.between(today, EXPIRY_DATE)
                log.info("过期邮箱功能还有 {} 天到期", daysLeft)
            }
            
        } catch (Exception e) {
            log.error("检查过期功能清理失败", e)
        }
    }

    /**
     * 清理过期邮箱功能
     */
    private void cleanupExpiredEmailFeature() {
        try {
            // 删除过期邮箱处理器文件
            Path handlerFile = Paths.get("src/main/groovy/com/bot/botmail/handler/ExpiredEmailHandler.groovy")
            if (Files.exists(handlerFile)) {
                Files.delete(handlerFile)
                log.info("已删除过期邮箱处理器文件: {}", handlerFile)
            }

            // 删除此清理服务文件（自删除）
            Path cleanupFile = Paths.get("src/main/groovy/com/bot/botmail/service/ExpiredFeatureCleanupService.groovy")
            if (Files.exists(cleanupFile)) {
                Files.delete(cleanupFile)
                log.info("已删除过期功能清理服务文件: {}", cleanupFile)
            }

            log.info("过期邮箱功能清理完成")
            
        } catch (Exception e) {
            log.error("清理过期邮箱功能失败", e)
        }
    }

    /**
     * 手动触发清理（用于测试）
     */
    public void manualCleanup() {
        log.info("手动触发过期功能清理")
        cleanupExpiredEmailFeature()
    }
}
