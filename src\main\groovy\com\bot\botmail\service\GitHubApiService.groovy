package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * GitHub API 服务
 */
@Service
@Slf4j
class GitHubApiService {

    private RestTemplate restTemplate = new RestTemplate()
    private static final String GITHUB_API_BASE = "https://api.github.com"

    /**
     * 获取仓库信息（包括 Stars 数量）
     */
    Map getRepoInfo(String owner, String repo) {
        try {
            log.info("获取 GitHub 仓库信息: {}/{}", owner, repo)

            String url = "${GITHUB_API_BASE}/repos/${owner}/${repo}"
            
            HttpHeaders headers = new HttpHeaders()
            headers.set("Accept", "application/vnd.github.v3+json")
            headers.set("User-Agent", "QQ-Bot")
            
            HttpEntity request = new HttpEntity<>(headers)
            
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map repoData = response.getBody()
            log.info("GitHub API 响应成功: stars={}", repoData?.stargazers_count)

            return [
                success: true,
                data: [
                    name: repoData?.name,
                    full_name: repoData?.full_name,
                    description: repoData?.description,
                    stars: repoData?.stargazers_count ?: 0,
                    forks: repoData?.forks_count ?: 0,
                    watchers: repoData?.watchers_count ?: 0,
                    language: repoData?.language,
                    html_url: repoData?.html_url,
                    created_at: repoData?.created_at,
                    updated_at: repoData?.updated_at
                ]
            ]

        } catch (Exception e) {
            log.error("获取 GitHub 仓库信息失败: {}/{}", owner, repo, e)
            return [
                success: false,
                error: "获取仓库信息失败: ${e.message}"
            ]
        }
    }

    /**
     * 获取指定仓库的 Stars 数量
     */
    Map getRepoStars(String owner, String repo) {
        Map result = getRepoInfo(owner, repo)
        if (result.success) {
            return [
                success: true,
                stars: result.data.stars,
                repo_name: result.data.full_name
            ]
        } else {
            return result
        }
    }

    /**
     * 获取 augment-token-idea-free 项目的 Stars
     */
    Map getAugmentTokenStars() {
        return getRepoStars("xn030523", "augment-token-idea-free")
    }

    /**
     * 格式化仓库信息为消息文本
     */
    String formatRepoInfo(Map repoData) {
        if (!repoData) return "❌ 无法获取仓库信息"

        return """⭐ GitHub 项目信息
━━━━━━━━━━━━━━━
📦 项目: ${repoData.name}
📝 描述: ${repoData.description ?: '暂无描述'}
⭐ Stars: ${repoData.stars}
🍴 Forks: ${repoData.forks}
👀 Watchers: ${repoData.watchers}
💻 语言: ${repoData.language ?: '未知'}
🔗 链接: ${repoData.html_url}
━━━━━━━━━━━━━━━"""
    }

    /**
     * 格式化 Stars 信息为统一UI格式
     */
    String formatStarsInfo(Map starsData) {
        if (!starsData || !starsData.success) {
            return "❌ 无法获取 Stars 信息"
        }

        return """⭐ GitHub Stars 信息
━━━━━━━━━━━━━━━
📦 项目: ${starsData.repo_name}
⭐ Stars: ${starsData.stars} 个
🔗 链接: https://github.com/${starsData.repo_name}
━━━━━━━━━━━━━━━"""
    }
}
