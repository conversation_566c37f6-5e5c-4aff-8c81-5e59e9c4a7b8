package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * CK激活码 API 服务
 */
@Service
@Slf4j
class CkApiService {

    private static final String API_BASE_URL = "https://augmentapi.159email.shop"
    private static final String CREATE_CK_ENDPOINT = "/api/public/ck/create"
    
    private RestTemplate restTemplate = new RestTemplate()

    /**
     * 创建一次性CK激活码
     */
    Map createCk(String notes = null) {
        try {
            log.info("开始创建CK激活码: notes={}", notes)

            // 构建请求URL
            String fullUrl = "${API_BASE_URL}${CREATE_CK_ENDPOINT}"
            
            // 构建请求体
            Map requestBody = [:]
            if (notes) {
                requestBody.notes = notes
            }
            
            // 构建请求头
            HttpHeaders headers = new HttpHeaders()
            headers.setContentType(MediaType.APPLICATION_JSON)
            headers.set("User-Agent", "QQ-Bot-CK-Generator")
            
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)
            
            // 发送请求
            ResponseEntity<Map> response = restTemplate.exchange(
                fullUrl,
                HttpMethod.POST,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("CK创建API响应: success={}, ckCode={}", result?.success, result?.data?.ck_code)

            if (result?.success) {
                return [
                    success: true,
                    data: result.data,
                    message: result.message ?: "CK创建成功"
                ]
            } else {
                return [
                    success: false,
                    error: result?.message ?: result?.error ?: "CK创建失败"
                ]
            }

        } catch (Exception e) {
            log.error("创建CK激活码失败: notes={}", notes, e)
            return [
                success: false,
                error: "CK创建失败: ${e.message}"
            ]
        }
    }

    /**
     * 格式化CK信息
     */
    String formatCkInfo(Map ckData, String userId) {
        if (!ckData) {
            return "❌ 无法获取CK信息"
        }

        String ckCode = ckData.ck_code ?: "未知"
        String status = ckData.status ?: "unknown"
        String createdAt = ckData.created_at ?: "未知"
        String notes = ckData.notes ?: "无备注"
        String ckId = ckData.id ?: "未知"

        return """🎫 CK激活码生成成功！
━━━━━━━━━━━━━━━
🔑 激活码: ${ckCode}
📋 状态: ${status == 'unused' ? '未使用' : status}
📅 创建时间: ${formatDateTime(createdAt)}
📝 备注: ${notes}
🆔 CK ID: ${ckId}
👤 生成者: ${userId}
━━━━━━━━━━━━━━━
⚠️ 请妥善保管激活码，仅可使用一次！"""
    }

    /**
     * 格式化日期时间
     */
    private String formatDateTime(String isoDateTime) {
        try {
            if (!isoDateTime) return "未知"
            
            // 简单处理ISO时间格式，提取日期和时间部分
            if (isoDateTime.contains("T")) {
                String[] parts = isoDateTime.split("T")
                String datePart = parts[0]
                String timePart = parts[1].split("\\.")[0] // 去掉毫秒部分
                return "${datePart} ${timePart}"
            }
            
            return isoDateTime
        } catch (Exception e) {
            log.warn("格式化日期时间失败: {}", isoDateTime, e)
            return isoDateTime
        }
    }
}
