package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.GitHubApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * GitHub Stars 事件处理器
 */
@Component
@Slf4j
class GitHubStarsHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private GitHubApiService gitHubService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理以下命令：
        // 看星星, 项目信息
        return messageText == "看星星" ||
               messageText == "项目信息"
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText().trim()
        String userId = event.getUserIdStr()
        
        log.info("收到 GitHub Stars 查询: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleStarsQuery(event, messageText)
        }
    }

    @Override
    int getPriority() {
        return 8 // 高优先级，在 MessageEventHandler 之前
    }

    /**
     * 处理 Stars 查询
     */
    private void handleStarsQuery(QQEvent event, String command) {
        try {
            sendReply(event, "🔍 正在查询 GitHub 项目信息...")

            if (command == "项目信息") {
                // 获取完整项目信息
                Map result = gitHubService.getRepoInfo("xn030523", "augment-token-idea-free")
                
                if (result.success) {
                    String repoInfo = gitHubService.formatRepoInfo(result.data)
                    sendReply(event, repoInfo)
                } else {
                    sendReply(event, "❌ 获取项目信息失败: ${result.error}")
                }
            } else if (command == "看星星") {
                // 只获取 Stars 信息
                Map result = gitHubService.getAugmentTokenStars()
                
                if (result.success) {
                    String starsInfo = gitHubService.formatStarsInfo(result)
                    sendReply(event, starsInfo)
                } else {
                    sendReply(event, "❌ 获取 Stars 信息失败: ${result.error}")
                }
            }

        } catch (Exception e) {
            log.error("处理 GitHub Stars 查询失败", e)
            sendReply(event, "❌ 查询过程中出现错误，请稍后再试")
        }
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
