package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.AuditApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * 审核功能处理器
 */
@Component
@Slf4j
class AuditHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private AuditApiService auditService

    @Value('${bot.admin-qq}')
    private String adminQq

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        return messageText.startsWith("审核 ")
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText().trim()
        String userId = event.getUserIdStr()
        
        log.info("收到审核请求: userId={}, command={}", userId, messageText)

        handleAuditCommand(event, messageText)
    }

    @Override
    int getPriority() {
        return 8 // 高优先级
    }

    /**
     * 处理审核命令
     */
    private void handleAuditCommand(QQEvent event, String messageText) {
        try {
            // 解析命令：审核 用户ID
            String[] parts = messageText.split(" ")
            if (parts.length != 2) {
                sendReply(event, "❌ 命令格式错误\n💡 正确格式：审核 用户ID")
                return
            }

            String userId = parts[1]

            // 检查用户是否存在且状态为pending
            Map checkResult = auditService.checkUserExists(userId)
            if (!checkResult.success) {
                sendReply(event, "❌ 检查用户失败: ${checkResult.error}")
                return
            }

            if (!checkResult.exists) {
                sendReply(event, "❌ 用户ID ${userId} 不在待审核列表中")
                return
            }

            // 获取用户信息
            def user = checkResult.user

            // 审核通过用户
            Map auditResult = auditService.approveUser(userId)
            if (auditResult.success) {
                // 获取管理员昵称
                String adminNickname = getAdminNickname(event)

                sendReply(event, """✅ 审核成功！
━━━━━━━━━━━━━━━
👤 用户ID: ${userId}
📧 邮箱: ${user?.email ?: '未知'}
👤 用户名: ${user?.username ?: '未知'}
📅 注册时间: ${user?.created_at ?: '未知'}
🎯 状态: 已激活 (active)
👮 审核员: ${adminNickname}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, "❌ 审核失败: ${auditResult.error}")
            }

        } catch (Exception e) {
            log.error("处理审核命令失败", e)
            sendReply(event, "❌ 审核命令处理失败: ${e.message}")
        }
    }

    /**
     * 获取管理员昵称
     */
    private String getAdminNickname(QQEvent event) {
        try {
            if (event.isGroupMessage()) {
                // 在群聊中获取管理员的群昵称
                def result = apiService.getGroupMemberInfo(event.getGroupIdStr(), adminQq)
                if (result.isSuccess() && result.data?.nickname) {
                    return result.data.nickname
                } else if (result.isSuccess() && result.data?.card) {
                    return result.data.card
                }
            }

            // 获取管理员的QQ昵称
            def result = apiService.getStrangerInfo(adminQq)
            if (result.isSuccess() && result.data?.nickname) {
                return result.data.nickname
            }

            // 如果都获取失败，返回默认值
            return "机器人管理员"
        } catch (Exception e) {
            log.warn("获取管理员昵称失败", e)
            return "机器人管理员"
        }
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
