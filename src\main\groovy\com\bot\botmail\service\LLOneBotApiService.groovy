package com.bot.botmail.service

import com.bot.botmail.entity.ApiResponse
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

@Service
@Slf4j
class LLOneBotApiService {

    @Value('${bot.llonebot.api-url}')
    private String apiUrl

    private RestTemplate restTemplate = new RestTemplate()

    // ==================== OneBot 11 消息相关 API ====================

    /**
     * 发送私聊消息
     */
    ApiResponse sendPrivateMessage(String userId, String message) {
        return callApi("/send_private_msg", [
            user_id: Long.parseLong(userId),
            message: createTextMessage(message)
        ])
    }

    /**
     * 发送群消息
     */
    ApiResponse sendGroupMessage(String groupId, String message) {
        return callApi("/send_group_msg", [
            group_id: Long.parseLong(groupId),
            message: createTextMessage(message)
        ])
    }

    /**
     * 发送群消息（支持复杂消息格式）
     */
    ApiResponse sendGroupMessageWithFormat(String groupId, List messageSegments) {
        return callApi("/send_group_msg", [
            group_id: Long.parseLong(groupId),
            message: messageSegments
        ])
    }

    /**
     * 发送私聊消息（支持复杂消息格式）
     */
    ApiResponse sendPrivateMessageWithFormat(String userId, List messageSegments) {
        return callApi("/send_private_msg", [
            user_id: Long.parseLong(userId),
            message: messageSegments
        ])
    }

    /**
     * 发送消息（自动判断私聊/群聊）
     */
    ApiResponse sendMessage(String messageType, String targetId, String message) {
        return callApi("/send_msg", [
            message_type: messageType,
            (messageType == "private" ? "user_id" : "group_id"): Long.parseLong(targetId),
            message: createTextMessage(message)
        ])
    }

    /**
     * 撤回消息
     */
    ApiResponse deleteMessage(String messageId) {
        return callApi("/delete_msg", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 获取消息
     */
    ApiResponse getMessage(String messageId) {
        return callApi("/get_msg", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 获取合并转发内容
     */
    ApiResponse getForwardMessage(String messageId) {
        return callApi("/get_forward_msg", [message_id: messageId])
    }

    /**
     * 发送好友赞
     */
    ApiResponse sendLike(String userId, int times = 1) {
        return callApi("/send_like", [
            user_id: Long.parseLong(userId),
            times: times
        ])
    }

    // ==================== OneBot 11 群管理 API ====================

    /**
     * 群组踢人
     */
    ApiResponse kickGroupMember(String groupId, String userId, boolean rejectAddRequest = false) {
        return callApi("/set_group_kick", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId),
            reject_add_request: rejectAddRequest
        ])
    }

    /**
     * 群单人禁言
     */
    ApiResponse banGroupMember(String groupId, String userId, int duration = 30 * 60) {
        return callApi("/set_group_ban", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId),
            duration: duration
        ])
    }

    /**
     * 群全员禁言
     */
    ApiResponse banAllGroupMembers(String groupId, boolean enable = true) {
        return callApi("/set_group_whole_ban", [
            group_id: Long.parseLong(groupId),
            enable: enable
        ])
    }

    /**
     * 设置群管理员
     */
    ApiResponse setGroupAdmin(String groupId, String userId, boolean enable = true) {
        return callApi("/set_group_admin", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId),
            enable: enable
        ])
    }

    /**
     * 设置群名片
     */
    ApiResponse setGroupCard(String groupId, String userId, String card) {
        return callApi("/set_group_card", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId),
            card: card
        ])
    }

    /**
     * 设置群名
     */
    ApiResponse setGroupName(String groupId, String groupName) {
        return callApi("/set_group_name", [
            group_id: Long.parseLong(groupId),
            group_name: groupName
        ])
    }

    /**
     * 退出群组
     */
    ApiResponse leaveGroup(String groupId, boolean isDismiss = false) {
        return callApi("/set_group_leave", [
            group_id: Long.parseLong(groupId),
            is_dismiss: isDismiss
        ])
    }

    /**
     * 处理加好友请求
     */
    ApiResponse setFriendAddRequest(String flag, boolean approve, String remark = "") {
        return callApi("/set_friend_add_request", [
            flag: flag,
            approve: approve,
            remark: remark
        ])
    }

    /**
     * 处理加群请求／邀请
     */
    ApiResponse setGroupAddRequest(String flag, String subType, boolean approve, String reason = "") {
        return callApi("/set_group_add_request", [
            flag: flag,
            sub_type: subType,
            approve: approve,
            reason: reason
        ])
    }

    // ==================== OneBot 11 信息获取 API ====================

    /**
     * 获取登录号信息
     */
    ApiResponse getLoginInfo() {
        return callApi("/get_login_info", [:])
    }

    /**
     * 获取陌生人信息
     */
    ApiResponse getStrangerInfo(String userId, boolean noCache = false) {
        return callApi("/get_stranger_info", [
            user_id: Long.parseLong(userId),
            no_cache: noCache
        ])
    }

    /**
     * 获取好友列表
     */
    ApiResponse getFriendList() {
        return callApi("/get_friend_list", [:])
    }

    /**
     * 获取群信息
     */
    ApiResponse getGroupInfo(String groupId, boolean noCache = false) {
        return callApi("/get_group_info", [
            group_id: Long.parseLong(groupId),
            no_cache: noCache
        ])
    }

    /**
     * 获取群列表
     */
    ApiResponse getGroupList() {
        return callApi("/get_group_list", [:])
    }

    /**
     * 获取群成员信息
     */
    ApiResponse getGroupMemberInfo(String groupId, String userId, boolean noCache = false) {
        return callApi("/get_group_member_info", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId),
            no_cache: noCache
        ])
    }

    /**
     * 获取群成员列表
     */
    ApiResponse getGroupMemberList(String groupId) {
        return callApi("/get_group_member_list", [
            group_id: Long.parseLong(groupId)
        ])
    }

    /**
     * 获取群荣誉信息
     */
    ApiResponse getGroupHonorInfo(String groupId, String type) {
        return callApi("/get_group_honor_info", [
            group_id: Long.parseLong(groupId),
            type: type
        ])
    }

    /**
     * 获取 Cookies
     */
    ApiResponse getCookies(String domain = "") {
        return callApi("/get_cookies", [domain: domain])
    }

    /**
     * 获取 CSRF Token
     */
    ApiResponse getCsrfToken() {
        return callApi("/get_csrf_token", [:])
    }

    /**
     * 获取 QQ 相关接口凭证
     */
    ApiResponse getCredentials(String domain = "") {
        return callApi("/get_credentials", [domain: domain])
    }

    /**
     * 获取语音
     */
    ApiResponse getRecord(String file, String outFormat) {
        return callApi("/get_record", [
            file: file,
            out_format: outFormat
        ])
    }

    /**
     * 获取图片信息
     */
    ApiResponse getImage(String file) {
        return callApi("/get_image", [file: file])
    }

    /**
     * 检查是否可以发送图片
     */
    ApiResponse canSendImage() {
        return callApi("/can_send_image", [:])
    }

    /**
     * 检查是否可以发送语音
     */
    ApiResponse canSendRecord() {
        return callApi("/can_send_record", [:])
    }

    /**
     * 获取状态
     */
    ApiResponse getStatus() {
        return callApi("/get_status", [:])
    }

    /**
     * 获取版本信息
     */
    ApiResponse getVersionInfo() {
        return callApi("/get_version_info", [:])
    }

    /**
     * 重启
     */
    ApiResponse setRestart(int delay = 0) {
        return callApi("/set_restart", [delay: delay])
    }

    /**
     * 清理缓存
     */
    ApiResponse cleanCache() {
        return callApi("/clean_cache", [:])
    }



    // ==================== Go-CQHTTP 扩展 API ====================

    /**
     * 设置登录号资料
     */
    ApiResponse setQQProfile(String nickname, String company, String email, String college, String personalNote) {
        return callApi("/set_qq_profile", [
            nickname: nickname,
            company: company,
            email: email,
            college: college,
            personal_note: personalNote
        ])
    }

    /**
     * 删除好友
     */
    ApiResponse deleteFriend(String userId) {
        return callApi("/delete_friend", [user_id: Long.parseLong(userId)])
    }

    /**
     * 标记消息已读
     */
    ApiResponse markMessageAsRead(String messageId) {
        return callApi("/mark_msg_as_read", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 发送合并转发 (群聊)
     */
    ApiResponse sendGroupForwardMessage(String groupId, List messages) {
        return callApi("/send_group_forward_msg", [
            group_id: Long.parseLong(groupId),
            messages: messages
        ])
    }

    /**
     * 发送合并转发 (好友)
     */
    ApiResponse sendPrivateForwardMessage(String userId, List messages) {
        return callApi("/send_private_forward_msg", [
            user_id: Long.parseLong(userId),
            messages: messages
        ])
    }

    /**
     * 获取群消息历史记录
     */
    ApiResponse getGroupMessageHistory(String groupId, String messageSeq = null) {
        Map params = [group_id: Long.parseLong(groupId)]
        if (messageSeq) params.message_seq = Long.parseLong(messageSeq)
        return callApi("/get_group_msg_history", params)
    }

    /**
     * 图片 OCR
     */
    ApiResponse ocrImage(String image) {
        return callApi("/ocr_image", [image: image])
    }

    /**
     * 获取群系统消息
     */
    ApiResponse getGroupSystemMessage() {
        return callApi("/get_group_system_msg", [:])
    }

    /**
     * 获取精华消息列表
     */
    ApiResponse getEssenceMessageList(String groupId) {
        return callApi("/get_essence_msg_list", [group_id: Long.parseLong(groupId)])
    }

    /**
     * 获取群 @全体成员 剩余次数
     */
    ApiResponse getGroupAtAllRemain(String groupId) {
        return callApi("/get_group_at_all_remain", [group_id: Long.parseLong(groupId)])
    }

    /**
     * 设置群头像
     */
    ApiResponse setGroupPortrait(String groupId, String file, int cache = 1) {
        return callApi("/set_group_portrait", [
            group_id: Long.parseLong(groupId),
            file: file,
            cache: cache
        ])
    }

    /**
     * 设置精华消息
     */
    ApiResponse setEssenceMessage(String messageId) {
        return callApi("/set_essence_msg", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 移出精华消息
     */
    ApiResponse deleteEssenceMessage(String messageId) {
        return callApi("/delete_essence_msg", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 发送群公告
     */
    ApiResponse sendGroupNotice(String groupId, String content, String image = "") {
        return callApi("/_send_group_notice", [
            group_id: Long.parseLong(groupId),
            content: content,
            image: image
        ])
    }

    /**
     * 获取群公告
     */
    ApiResponse getGroupNotice(String groupId) {
        return callApi("/_get_group_notice", [group_id: Long.parseLong(groupId)])
    }

    // ==================== 文件相关 API ====================

    /**
     * 上传群文件
     */
    ApiResponse uploadGroupFile(String groupId, String file, String name, String folder = "/") {
        return callApi("/upload_group_file", [
            group_id: Long.parseLong(groupId),
            file: file,
            name: name,
            folder: folder
        ])
    }

    /**
     * 删除群文件
     */
    ApiResponse deleteGroupFile(String groupId, String fileId, int busid) {
        return callApi("/delete_group_file", [
            group_id: Long.parseLong(groupId),
            file_id: fileId,
            busid: busid
        ])
    }

    /**
     * 创建群文件文件夹
     */
    ApiResponse createGroupFileFolder(String groupId, String name, String parentId = "/") {
        return callApi("/create_group_file_folder", [
            group_id: Long.parseLong(groupId),
            name: name,
            parent_id: parentId
        ])
    }

    /**
     * 删除群文件文件夹
     */
    ApiResponse deleteGroupFolder(String groupId, String folderId) {
        return callApi("/delete_group_folder", [
            group_id: Long.parseLong(groupId),
            folder_id: folderId
        ])
    }

    /**
     * 获取群文件系统信息
     */
    ApiResponse getGroupFileSystemInfo(String groupId) {
        return callApi("/get_group_file_system_info", [group_id: Long.parseLong(groupId)])
    }

    /**
     * 获取群根目录文件列表
     */
    ApiResponse getGroupRootFiles(String groupId) {
        return callApi("/get_group_root_files", [group_id: Long.parseLong(groupId)])
    }

    /**
     * 获取群子目录文件列表
     */
    ApiResponse getGroupFilesByFolder(String groupId, String folderId) {
        return callApi("/get_group_files_by_folder", [
            group_id: Long.parseLong(groupId),
            folder_id: folderId
        ])
    }

    /**
     * 获取群文件资源链接
     */
    ApiResponse getGroupFileUrl(String groupId, String fileId, int busid) {
        return callApi("/get_group_file_url", [
            group_id: Long.parseLong(groupId),
            file_id: fileId,
            busid: busid
        ])
    }

    /**
     * 上传私聊文件
     */
    ApiResponse uploadPrivateFile(String userId, String file, String name) {
        return callApi("/upload_private_file", [
            user_id: Long.parseLong(userId),
            file: file,
            name: name
        ])
    }

    /**
     * 下载文件到缓存目录
     */
    ApiResponse downloadFile(String url, int threadCount = 1, Map headers = [:]) {
        return callApi("/download_file", [
            url: url,
            thread_count: threadCount,
            headers: headers
        ])
    }

    /**
     * 对事件执行快速操作
     */
    ApiResponse handleQuickOperation(String context, String operation) {
        return callApi("/.handle_quick_operation", [
            context: context,
            operation: operation
        ])
    }

    // ==================== LLOneBot 专有 API ====================

    /**
     * 设置头像
     */
    ApiResponse setQQAvatar(String file) {
        return callApi("/set_qq_avatar", [file: file])
    }

    /**
     * 获取文件信息
     */
    ApiResponse getFile(String fileId) {
        return callApi("/get_file", [file_id: fileId])
    }

    /**
     * 转发单条信息到私聊
     */
    ApiResponse forwardFriendSingleMessage(String userId, String messageId) {
        return callApi("/forward_friend_single_msg", [
            user_id: Long.parseLong(userId),
            message_id: Long.parseLong(messageId)
        ])
    }

    /**
     * 转发单条信息到群聊
     */
    ApiResponse forwardGroupSingleMessage(String groupId, String messageId) {
        return callApi("/forward_group_single_msg", [
            group_id: Long.parseLong(groupId),
            message_id: Long.parseLong(messageId)
        ])
    }

    /**
     * 设置消息的表情回应
     */
    ApiResponse setMessageEmojiLike(String messageId, String emojiId) {
        return callApi("/set_msg_emoji_like", [
            message_id: Long.parseLong(messageId),
            emoji_id: emojiId
        ])
    }

    /**
     * 获取好友分类列表
     */
    ApiResponse getFriendsWithCategory() {
        return callApi("/get_friends_with_category", [:])
    }

    /**
     * 设置自身在线状态
     */
    ApiResponse setOnlineStatus(int status, String extStatus = "", int batteryStatus = 0) {
        return callApi("/set_online_status", [
            status: status,
            ext_status: extStatus,
            battery_status: batteryStatus
        ])
    }

    /**
     * 获取自身点赞列表
     */
    ApiResponse getProfileLike() {
        return callApi("/get_profile_like", [:])
    }

    /**
     * 发送合并转发消息
     */
    ApiResponse sendForwardMessage(List messages) {
        return callApi("/send_forward_msg", [messages: messages])
    }

    /**
     * 获取收藏表情
     */
    ApiResponse fetchCustomFace() {
        return callApi("/fetch_custom_face", [:])
    }

    /**
     * 获取好友历史消息记录
     */
    ApiResponse getFriendMessageHistory(String userId, String messageSeq = null, int count = 20) {
        Map params = [
            user_id: Long.parseLong(userId),
            count: count
        ]
        if (messageSeq) params.message_seq = Long.parseLong(messageSeq)
        return callApi("/get_friend_msg_history", params)
    }

    /**
     * 获取表情回应列表
     */
    ApiResponse fetchEmojiLike(String messageId) {
        return callApi("/fetch_emoji_like", [message_id: Long.parseLong(messageId)])
    }

    /**
     * 获取官方机器人 QQ 号区间
     */
    ApiResponse getRobotUinRange() {
        return callApi("/get_robot_uin_range", [:])
    }

    /**
     * 好友戳一戳
     */
    ApiResponse friendPoke(String userId) {
        return callApi("/friend_poke", [user_id: Long.parseLong(userId)])
    }

    /**
     * 群组戳一戳
     */
    ApiResponse groupPoke(String groupId, String userId) {
        return callApi("/group_poke", [
            group_id: Long.parseLong(groupId),
            user_id: Long.parseLong(userId)
        ])
    }

    /**
     * 设置好友备注
     */
    ApiResponse setFriendRemark(String userId, String remark) {
        return callApi("/set_friend_remark", [
            user_id: Long.parseLong(userId),
            remark: remark
        ])
    }

    /**
     * 移动好友到分组
     */
    ApiResponse setFriendCategory(String userId, String categoryName) {
        return callApi("/set_friend_category", [
            user_id: Long.parseLong(userId),
            category_name: categoryName
        ])
    }

    /**
     * 设置群备注
     */
    ApiResponse setGroupRemark(String groupId, String remark) {
        return callApi("/set_group_remark", [
            group_id: Long.parseLong(groupId),
            remark: remark
        ])
    }

    /**
     * 设置群消息接收方式
     */
    ApiResponse setGroupMessageMask(String groupId, int mask) {
        return callApi("/set_group_msg_mask", [
            group_id: Long.parseLong(groupId),
            mask: mask
        ])
    }

    /**
     * 移动群文件
     */
    ApiResponse moveGroupFile(String groupId, String fileId, String parentDirectory) {
        return callApi("/move_group_file", [
            group_id: Long.parseLong(groupId),
            file_id: fileId,
            parent_directory: parentDirectory
        ])
    }

    /**
     * 获取群禁言列表
     */
    ApiResponse getGroupShutList(String groupId) {
        return callApi("/get_group_shut_list", [group_id: Long.parseLong(groupId)])
    }

    /**
     * 重命名群文件文件夹名
     */
    ApiResponse renameGroupFileFolder(String groupId, String folderId, String folderName) {
        return callApi("/rename_group_file_folder", [
            group_id: Long.parseLong(groupId),
            folder_id: folderId,
            folder_name: folderName
        ])
    }

    // ==================== 工具方法 ====================

    /**
     * 创建文本消息格式
     */
    private List createTextMessage(String text) {
        return [[
            type: "text",
            data: [text: text]
        ]]
    }

    /**
     * 创建图片消息格式
     */
    List createImageMessage(String file) {
        return [[
            type: "image",
            data: [file: file]
        ]]
    }

    /**
     * 创建语音消息格式
     */
    List createRecordMessage(String file) {
        return [[
            type: "record",
            data: [file: file]
        ]]
    }

    /**
     * 创建视频消息格式
     */
    List createVideoMessage(String file) {
        return [[
            type: "video",
            data: [file: file]
        ]]
    }

    /**
     * 创建 @ 消息格式
     */
    List createAtMessage(String qq) {
        return [[
            type: "at",
            data: [qq: qq]
        ]]
    }

    /**
     * 创建混合消息格式
     */
    List createMixedMessage(List<Map> segments) {
        return segments
    }

    /**
     * 调用 API 的通用方法
     */
    private ApiResponse callApi(String endpoint, Map params) {
        try {
            String url = "${apiUrl}${endpoint}"
            log.info("调用 LLOneBot API: {} with params: {}", endpoint, params)

            Map response = restTemplate.postForObject(url, params, Map.class)
            log.info("API 响应: {}", response)

            // 转换为统一的响应格式
            ApiResponse apiResponse = new ApiResponse()
            apiResponse.status = response?.status ?: "ok"
            apiResponse.retcode = response?.retcode ?: 0
            apiResponse.data = response?.data
            apiResponse.message = response?.message
            apiResponse.echo = response?.echo

            return apiResponse
        } catch (Exception e) {
            log.error("调用 API 失败: {} - {}", endpoint, e.message)
            return ApiResponse.failed("API调用失败: ${e.message}")
        }
    }
}
