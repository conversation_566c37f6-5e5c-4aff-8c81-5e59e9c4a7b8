package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 舔狗日记 API 服务
 */
@Service
@Slf4j
class DogApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取随机舔狗日记
     */
    Map getDogDiary() {
        Map result = xxApiService.getDogDiary()
        if (result.success) {
            return [
                success: true,
                diary: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }

    /**
     * 格式化舔狗日记
     */
    String formatDogDiary(String diary) {
        return xxApiService.formatDogDiary(diary)
    }
}
