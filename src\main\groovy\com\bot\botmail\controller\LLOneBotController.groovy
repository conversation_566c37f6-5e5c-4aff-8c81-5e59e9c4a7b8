package com.bot.botmail.controller

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.entity.BotResponse
import com.bot.botmail.service.EventDispatcher
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@Slf4j
class LLOneBotController {

    @Autowired
    private EventDispatcher eventDispatcher

    /**
     * 接收 LLOneBot 发送的事件
     */
    @PostMapping("/")
    Map handleEvent(@RequestBody QQEvent event) {
        log.info("收到事件: type={}, subType={}", event.post_type, event.message_type ?: event.notice_type ?: event.request_type)

        try {
            // 使用事件分发器处理事件
            eventDispatcher.dispatch(event)
        } catch (Exception e) {
            log.error("事件分发失败", e)
            return BotResponse.error("事件处理失败: ${e.message}").toMap()
        }

        return BotResponse.ok().toMap()
    }

    /**
     * 获取事件处理器信息
     */
    @GetMapping("/handlers")
    Map getHandlers() {
        try {
            Map handlerStats = eventDispatcher.getHandlerStats()
            return BotResponse.ok(handlerStats, "获取处理器信息成功").toMap()
        } catch (Exception e) {
            log.error("获取处理器信息失败", e)
            return BotResponse.error("获取处理器信息失败: ${e.message}").toMap()
        }
    }
}
