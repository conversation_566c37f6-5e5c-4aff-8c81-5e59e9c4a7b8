package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 黑丝图片 API 服务
 */
@Service
@Slf4j
class HeisiImageApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取随机黑丝图片
     */
    Map getRandomHeisiImage() {
        Map result = xxApiService.getHeisiImage()
        if (result.success) {
            return [
                success: true,
                imageUrl: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }
}
