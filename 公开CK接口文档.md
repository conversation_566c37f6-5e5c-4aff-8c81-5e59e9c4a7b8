# 公开CK接口文档

## 概述

本文档描述了一次性CK（激活码）系统的公开接口，供第三方软件接入使用。

**基础信息：**
- 接口域名：`https://augmentapi.159email.shop` (生产环境)
- 接口前缀：`/api/public/ck`
- 数据格式：JSON
- 字符编码：UTF-8

## 接口列表

### 1. 创建一次性CK

**接口地址：** `POST /api/public/ck/create`

**功能说明：** 创建一个新的一次性激活码

**请求参数：**
```json
{
  "notes": "备注信息（可选）"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| notes | string | 否 | 备注信息，最大长度255字符 |

**请求示例：**
```bash
curl -X POST http://localhost:3001/api/public/ck/create \
  -H "Content-Type: application/json" \
  -d '{"notes": "测试CK"}'
```

**成功响应：**
```json
{
  "success": true,
  "data": {
    "id": "29cecdc6-b216-420a-ae63-b450f58f5f8c",
    "ck_code": "ZC9U-V8J8-6EBO-B6Q6",
    "status": "unused",
    "created_at": "2025-08-03T15:18:27.000Z",
    "notes": "测试CK"
  },
  "message": "一次性CK创建成功"
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "创建CK失败",
  "error": "具体错误信息"
}
```

---

### 2. 验证并消耗CK

**接口地址：** `POST /api/public/ck/verify`

**功能说明：** 验证CK码的有效性并将其标记为已使用（一次性消耗）

**请求参数：**
```json
{
  "key_code": "ZC9U-V8J8-6EBO-B6Q6",
  "device_id": "设备标识（可选）"
}
```

**参数说明：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| key_code | string | 是 | CK码，格式：XXXX-XXXX-XXXX-XXXX |
| device_id | string | 否 | 设备标识，用于记录使用设备 |

**请求示例：**
```bash
curl -X POST http://localhost:3001/api/public/ck/verify \
  -H "Content-Type: application/json" \
  -d '{"key_code": "ZC9U-V8J8-6EBO-B6Q6", "device_id": "PC001"}'
```

**成功响应（CK有效且未使用）：**
```json
{
  "success": true,
  "data": {
    "valid": true,
    "ck_info": {
      "id": "29cecdc6-b216-420a-ae63-b450f58f5f8c",
      "ck_code": "ZC9U-V8J8-6EBO-B6Q6",
      "status": "used",
      "created_at": "2025-08-03T15:18:27.000Z",
      "used_at": "2025-08-03T15:25:30.000Z",
      "notes": "测试CK"
    }
  },
  "message": "CK验证成功"
}
```

**失败响应（CK无效）：**
```json
{
  "success": false,
  "message": "CK码不存在或格式错误"
}
```

**失败响应（CK已使用）：**
```json
{
  "success": false,
  "message": "CK码已被使用",
  "data": {
    "valid": false,
    "used_at": "2025-08-03T15:25:30.000Z"
  }
}

## 注意事项

1. **一次性使用**：每个CK码只能使用一次，验证成功后会自动标记为已使用
2. **格式要求**：CK码格式固定为 `XXXX-XXXX-XXXX-XXXX`
3. **并发安全**：接口支持并发访问，使用数据库事务保证数据一致性
4. **错误处理**：请根据返回的 `success` 字段判断操作是否成功
5. **日志记录**：所有操作都会记录日志，包括IP地址和时间戳

## 技术支持

如有问题请联系技术支持团队。
