package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.GoMailApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletableFuture
import java.util.regex.Pattern

/**
 * 过期邮箱处理器 - 临时功能，7月5日到期
 */
@Component
@Slf4j
class ExpiredEmailHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private GoMailApiService goMailService

    private Random random = new Random()

    // 过期日期：2025年7月5日
    private static final LocalDate EXPIRY_DATE = LocalDate.of(2025, 7, 5)
    private static final String EMAIL_DOMAIN = "daiju.cyou"

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        return messageText == "过期邮箱" || messageText.startsWith("过期邮箱 ")
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()

        log.info("收到过期邮箱请求: userId={}, message={}", userId, messageText)

        // 检查是否已过期
        if (LocalDate.now().isAfter(EXPIRY_DATE)) {
            sendReply(event, """❌ 过期邮箱功能已到期
━━━━━━━━━━━━━━━
📅 到期日期: ${EXPIRY_DATE.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}
💡 请使用其他邮箱功能
━━━━━━━━━━━━━━━""")
            return
        }

        // 解析自定义前缀
        String customPrefix = null
        if (messageText.startsWith("过期邮箱 ")) {
            customPrefix = messageText.substring(5).trim()
            if (customPrefix.isEmpty()) {
                sendReply(event, """❌ 自定义前缀不能为空
━━━━━━━━━━━━━━━
💡 正确格式：过期邮箱 自定义前缀
📝 例如：过期邮箱 myname123
━━━━━━━━━━━━━━━""")
                return
            }

            // 验证前缀格式（只允许字母数字和连字符）
            if (!customPrefix.matches(/^[a-zA-Z0-9\-]+$/)) {
                sendReply(event, """❌ 前缀格式不正确
━━━━━━━━━━━━━━━
💡 只允许字母、数字和连字符(-)
📝 例如：过期邮箱 myname123
━━━━━━━━━━━━━━━""")
                return
            }
        }

        sendReply(event, "🔄 正在创建过期邮箱，请稍候...")

        CompletableFuture.runAsync {
            try {
                // 创建过期邮箱 - 使用GoMail真实接口
                Map mailboxResult
                if (customPrefix) {
                    // 自定义前缀邮箱
                    mailboxResult = goMailService.createCustomMailbox(customPrefix, EMAIL_DOMAIN)
                } else {
                    // 随机前缀邮箱
                    mailboxResult = goMailService.createMailbox(EMAIL_DOMAIN)
                }

                if (!mailboxResult.success) {
                    sendReply(event, "❌ 创建过期邮箱失败: ${mailboxResult.error}")
                    return
                }

                String emailAddress = mailboxResult.data.email
                
                String emailType = customPrefix ? "自定义前缀过期邮箱" : "随机过期邮箱"
                sendReply(event, """📧 过期邮箱创建成功！
━━━━━━━━━━━━━━━
📮 邮箱地址: ${emailAddress}
🏷️ 类型: ${emailType}
⏰ 有效期: 24小时
🚫 功能到期: ${EXPIRY_DATE.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日"))}
🔍 开始监控邮件... (60秒内持续检查)
━━━━━━━━━━━━━━━""")

                // 监控邮件（60秒）
                long startTime = System.currentTimeMillis()
                long timeout = 60000 // 60秒
                boolean found = false
                int checkCount = 0

                while (System.currentTimeMillis() - startTime < timeout && !found) {
                    checkCount++

                    // 每3秒检查一次
                    if (checkCount > 1) {
                        Thread.sleep(3000)
                    }

                    log.info("第 {} 次检查过期邮箱: {}", checkCount, emailAddress)

                    // 获取邮件列表
                    Map emailsResult = goMailService.getEmails(emailAddress)
                    if (!emailsResult.success) {
                        log.warn("获取过期邮件失败: {}", emailsResult.error)
                        continue
                    }

                    List emails = emailsResult.data?.emails
                    if (!emails || emails.isEmpty()) {
                        log.info("第 {} 次检查: 暂无过期邮件", checkCount)
                        continue
                    }

                    // 获取第一封邮件详情
                    String firstEmailId = emails[0].id
                    Map emailDetail = goMailService.getEmailDetail(firstEmailId)

                    if (!emailDetail.success) {
                        log.warn("获取过期邮件详情失败: {}", emailDetail.error)
                        continue
                    }

                    // 提取验证码
                    String subject = emailDetail.data?.subject ?: ""
                    String textContent = emailDetail.data?.textContent ?: ""
                    String htmlContent = emailDetail.data?.htmlContent ?: ""

                    String verificationCode = extractVerificationCode(subject, textContent, htmlContent)

                    if (verificationCode) {
                        found = true
                        long elapsedTime = (System.currentTimeMillis() - startTime) / 1000
                        
                        sendReply(event, """✅ 过期邮箱验证码获取成功！
━━━━━━━━━━━━━━━
📧 邮箱: ${emailAddress}
📨 主题: ${subject}
🔑 验证码: ${verificationCode}
⏱️ 用时: ${elapsedTime}秒
🚫 功能到期: ${EXPIRY_DATE.format(DateTimeFormatter.ofPattern("MM月dd日"))}
━━━━━━━━━━━━━━━""")
                    } else {
                        log.info("第 {} 次检查: 收到过期邮件但未找到验证码，主题: {}", checkCount, subject)
                        // 静默继续检查，不发送消息
                    }
                }

                // 如果60秒内没有找到验证码
                if (!found) {
                    sendReply(event, """⏰ 过期邮箱监控超时 (60秒)
━━━━━━━━━━━━━━━
📧 邮箱: ${emailAddress}
📭 结果: 未收到包含验证码的邮件
💡 建议: 重新获取过期邮箱或稍后再试
🚫 功能到期: ${EXPIRY_DATE.format(DateTimeFormatter.ofPattern("MM月dd日"))}
━━━━━━━━━━━━━━━""")
                }

            } catch (Exception e) {
                log.error("处理过期邮箱请求失败", e)
                sendReply(event, "❌ 过期邮箱功能暂时不可用，请稍后再试")
            }
        }
    }

    @Override
    int getPriority() {
        return 8 // 高优先级
    }



    /**
     * 提取验证码
     */
    private String extractVerificationCode(String subject, String textContent, String htmlContent) {
        // 常见验证码模式
        List<Pattern> patterns = [
            Pattern.compile("验证码[：:]?\\s*([0-9]{4,8})"),
            Pattern.compile("verification code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("([0-9]{6})"),
            Pattern.compile("([0-9]{4})")
        ]

        String allContent = "${subject} ${textContent} ${htmlContent}"

        for (Pattern pattern : patterns) {
            def matcher = pattern.matcher(allContent)
            if (matcher.find()) {
                return matcher.group(1)
            }
        }

        return null
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
