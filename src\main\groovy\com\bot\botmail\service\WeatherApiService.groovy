package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 天气 API 服务
 */
@Service
@Slf4j
class WeatherApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取天气信息
     */
    Map getWeatherInfo(String city = null) {
        Map result = xxApiService.getWeatherInfo(city)
        if (result.success) {
            return [
                success: true,
                weatherData: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }

    /**
     * 格式化天气信息
     */
    String formatWeatherInfo(Map weatherData) {
        if (!weatherData) {
            return "❌ 无法获取天气信息"
        }

        String city = weatherData.city ?: "未知城市"
        List<Map> data = weatherData.data ?: []

        if (data.isEmpty()) {
            return "❌ 天气数据为空"
        }

        StringBuilder weatherText = new StringBuilder()
        weatherText.append("🌤️ ${city} 天气预报\n")
        weatherText.append("━━━━━━━━━━━━━━━\n")

        data.eachWithIndex { dayWeather, index ->
            if (index < 3) { // 只显示前3天
                weatherText.append("📅 ${dayWeather.date ?: '未知'}\n")
                weatherText.append("🌡️ 温度: ${dayWeather.temperature ?: '未知'}\n")
                weatherText.append("☁️ 天气: ${dayWeather.weather ?: '未知'}\n")
                weatherText.append("💨 风力: ${dayWeather.wind ?: '未知'}\n")
                weatherText.append("🌬️ 空气: ${dayWeather.air_quality ?: '未知'}\n")
                if (index < 2) {
                    weatherText.append("━━━━━━━━━━━━━━━\n")
                }
            }
        }

        weatherText.append("━━━━━━━━━━━━━━━")
        return weatherText.toString()
    }
}
