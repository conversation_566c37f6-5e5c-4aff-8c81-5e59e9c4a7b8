package com.bot.botmail.service

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.handler.EventHandler
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 事件分发器
 */
@Service
@Slf4j
class EventDispatcher {

    @Autowired
    private List<EventHandler> eventHandlers

    /**
     * 分发事件到对应的处理器
     */
    void dispatch(QQEvent event) {
        log.info("开始分发事件: {}, 消息内容: '{}'", event.post_type, event.getMessageText())

        try {
            // 按优先级排序处理器
            List<EventHandler> sortedHandlers = eventHandlers.sort { it.getPriority() }
            log.info("可用处理器数量: {}", sortedHandlers.size())

            // 查找能处理该事件的处理器
            EventHandler handler = sortedHandlers.find { it.canHandle(event) }

            if (handler) {
                log.info("使用处理器 {} 处理事件", handler.class.simpleName)
                handler.handle(event)
            } else {
                log.info("未找到合适的处理器处理事件: {}, 消息: '{}'", event.post_type, event.getMessageText())
            }
        } catch (Exception e) {
            log.error("事件处理失败", e)
        }
    }

    /**
     * 获取所有已注册的处理器
     */
    List<EventHandler> getRegisteredHandlers() {
        return eventHandlers.sort { it.getPriority() }
    }

    /**
     * 获取处理器统计信息
     */
    Map getHandlerStats() {
        Map stats = [:]
        eventHandlers.each { handler ->
            stats[handler.class.simpleName] = [
                priority: handler.getPriority(),
                className: handler.class.name
            ]
        }
        return stats
    }
}
