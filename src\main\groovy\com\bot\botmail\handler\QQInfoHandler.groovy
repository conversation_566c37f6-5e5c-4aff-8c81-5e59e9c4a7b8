package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.QQInfoApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * QQ信息查询 事件处理器
 */
@Component
@Slf4j
class QQInfoHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private QQInfoApiService qqInfoService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理命令：看看自己几斤几两
        return messageText == "看看自己几斤几两"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()
        
        log.info("收到QQ信息查询请求: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleQQInfoRequest(event)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理QQ信息查询请求
     */
    private void handleQQInfoRequest(QQEvent event) {
        try {
            String userQQ = event.getUserIdStr()
            
            sendReply(event, "⚖️ 正在称称你有几斤几两...")

            // 调用API获取QQ信息
            Map result = qqInfoService.getQQInfo(userQQ)
            
            if (result.success) {
                Map qqInfo = result.data
                String formattedInfo = qqInfoService.formatQQInfo(qqInfo, userQQ)
                
                sendReply(event, formattedInfo)
                
                log.info("QQ信息查询成功: userId={}", userQQ)
            } else {
                sendReply(event, "❌ 获取QQ信息失败: ${result.error}")
                log.error("获取QQ信息失败: userId={}, error={}", userQQ, result.error)
            }

        } catch (Exception e) {
            log.error("处理QQ信息查询请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
