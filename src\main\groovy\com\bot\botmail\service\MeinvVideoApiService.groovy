package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 美女视频 API 服务
 */
@Service
@Slf4j
class MeinvVideoApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取随机美女视频
     */
    Map getRandomMeinvVideo() {
        Map result = xxApiService.getMeinvVideo()
        if (result.success) {
            return [
                success: true,
                videoUrl: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }
}
