package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * 审核系统 API 服务
 */
@Service
@Slf4j
class AuditApiService {

    @Value('${audit.api.base-url}')
    private String baseUrl

    @Value('${audit.api.token}')
    private String apiToken

    private RestTemplate restTemplate = new RestTemplate()

    /**
     * 获取待审核用户列表
     */
    Map getPendingUsers() {
        try {
            log.info("获取待审核用户列表")

            HttpHeaders headers = createHeaders()
            HttpEntity request = new HttpEntity<>(headers)

            String url = "${baseUrl}/api/admin/users?status=pending"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("获取待审核用户列表响应: 用户数量={}", result?.users?.size() ?: 0)

            return [
                success: true,
                data: result
            ]
        } catch (Exception e) {
            log.error("获取待审核用户列表失败", e)
            return [
                success: false,
                error: "获取用户列表失败: ${e.message}"
            ]
        }
    }

    /**
     * 审核用户（通过）
     */
    Map approveUser(String userId) {
        return updateUserStatus(userId, "active")
    }

    /**
     * 拒绝用户
     */
    Map rejectUser(String userId) {
        return updateUserStatus(userId, "disabled")
    }

    /**
     * 更新用户状态
     */
    Map updateUserStatus(String userId, String status) {
        try {
            log.info("更新用户状态: userId={}, status={}", userId, status)

            // 构建请求体
            Map requestBody = [
                user_id: Integer.parseInt(userId),
                status: status
            ]

            // 构建请求头
            HttpHeaders headers = createHeaders()
            HttpEntity<Map> request = new HttpEntity<>(requestBody, headers)

            // 发送请求
            String url = "${baseUrl}/api/admin/users"
            ResponseEntity<Map> response = restTemplate.exchange(
                url,
                HttpMethod.PUT,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("更新用户状态响应: {}", result)

            return [
                success: true,
                data: result
            ]
        } catch (Exception e) {
            log.error("更新用户状态失败: userId={}, status={}", userId, status, e)
            return [
                success: false,
                error: "更新用户状态失败: ${e.message}"
            ]
        }
    }

    /**
     * 检查用户是否存在
     */
    Map checkUserExists(String userId) {
        try {
            log.info("检查用户是否存在: userId={}", userId)

            // 获取待审核用户列表
            Map pendingResult = getPendingUsers()
            if (!pendingResult.success) {
                return pendingResult
            }

            // 检查用户是否在待审核列表中
            List users = pendingResult.data?.users ?: []
            boolean userExists = users.any { user ->
                user.id?.toString() == userId
            }

            if (userExists) {
                def user = users.find { it.id?.toString() == userId }
                return [
                    success: true,
                    exists: true,
                    user: user
                ]
            } else {
                return [
                    success: true,
                    exists: false,
                    message: "用户ID ${userId} 不在待审核列表中"
                ]
            }

        } catch (Exception e) {
            log.error("检查用户是否存在失败: userId={}", userId, e)
            return [
                success: false,
                error: "检查用户失败: ${e.message}"
            ]
        }
    }

    /**
     * 创建请求头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders()
        headers.set("X-Auth-Token", apiToken)
        headers.set("Content-Type", "application/json")
        return headers
    }
}
