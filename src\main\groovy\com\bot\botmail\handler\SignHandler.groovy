package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.UserSignService
import com.bot.botmail.service.CkApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 签到功能处理器
 */
@Component
@Slf4j
class SignHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private UserSignService signService

    @Autowired
    private CkApiService ckApiService

    @Value('${bot.admin-qq}')
    private String adminQq

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理签到相关命令
        return messageText == "签到" ||
               messageText == "查询邮箱次数" ||
               messageText == "签到排行榜" ||
               messageText == "生成ck" ||
               messageText == "查询ck次数" ||
               (event.message instanceof List && hasAtAndAddEmail(event.message)) ||
               (event.message instanceof List && hasAtAndCheckEmail(event.message)) ||
               (event.message instanceof List && hasAtAndAddCk(event.message)) ||
               (event.message instanceof List && hasAtAndCheckCk(event.message))
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText().trim()
        String userId = event.getUserIdStr()
        
        log.info("收到签到相关请求: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            if (messageText == "签到") {
                handleSignIn(event)
            } else if (messageText == "查询邮箱次数") {
                handleCheckEmailCount(event)
            } else if (messageText == "签到排行榜") {
                handleSignRanking(event)
            } else if (messageText == "生成ck") {
                handleGenerateCk(event)
            } else if (messageText == "查询ck次数") {
                handleCheckCkCount(event)
            } else if (event.message instanceof List && hasAtAndAddEmail(event.message)) {
                handleAddEmailCount(event, messageText)
            } else if (event.message instanceof List && hasAtAndCheckEmail(event.message)) {
                handleCheckOtherEmailCount(event, messageText)
            } else if (event.message instanceof List && hasAtAndAddCk(event.message)) {
                handleAddCkCount(event, messageText)
            } else if (event.message instanceof List && hasAtAndCheckCk(event.message)) {
                handleCheckOtherCkCount(event, messageText)
            }
        }
    }

    @Override
    int getPriority() {
        return 15 // 较低优先级，让禁言踢人等管理命令优先处理
    }

    /**
     * 处理签到
     */
    private void handleSignIn(QQEvent event) {
        try {
            String userId = event.getUserIdStr()
            Map result = signService.userSignIn(userId)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """✅ 签到成功！
━━━━━━━━━━━━━━━
📅 签到日期: 今天
🎯 连续签到: ${userData.totalSignDays} 天
📧 邮箱次数: +5 (总计: ${userData.remainingEmailCount})
🏆 排名: 努力爬升中...
━━━━━━━━━━━━━━━""")
            } else {
                Map userData = result.data
                sendReply(event, """📅 今日已签到！
━━━━━━━━━━━━━━━
🎯 连续签到: ${userData?.totalSignDays ?: 0} 天
📧 剩余次数: ${userData?.remainingEmailCount ?: 0}
💡 明天再来签到吧~
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理签到失败", e)
            sendReply(event, "❌ 签到失败，请稍后再试")
        }
    }

    /**
     * 处理查询邮箱次数
     */
    private void handleCheckEmailCount(QQEvent event) {
        try {
            String userId = event.getUserIdStr()
            Map result = signService.checkEmailCount(userId)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """📧 邮箱使用次数查询
━━━━━━━━━━━━━━━
👤 用户: ${userId}
📧 剩余次数: ${result.remainingCount}
🎯 总签到天数: ${userData?.totalSignDays ?: 0}
📅 最后签到: ${userData?.lastSignDate ?: '未签到'}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, """📧 邮箱使用次数查询
━━━━━━━━━━━━━━━
❌ ${result.message}
💡 发送"签到"获取邮箱使用次数
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理查询邮箱次数失败", e)
            sendReply(event, "❌ 查询失败，请稍后再试")
        }
    }

    /**
     * 处理查询其他用户邮箱次数（管理员专用）
     */
    private void handleCheckOtherEmailCount(QQEvent event, String messageText) {
        try {
            // 检查是否为管理员
            if (!isAdmin(event)) {
                sendReply(event, "❌ 只有管理员可以查询其他用户的邮箱次数")
                return
            }

            // 从消息中提取被@的用户ID
            String targetUserId = null
            if (event.message instanceof List) {
                for (segment in event.message) {
                    if (segment instanceof Map && segment.type == "at") {
                        targetUserId = segment.data?.qq?.toString()
                        break
                    }
                }
            }

            if (!targetUserId) {
                sendReply(event, "❌ 请@要查询的用户\n💡 格式：@用户 查询邮箱次数")
                return
            }

            Map result = signService.checkEmailCount(targetUserId)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """📧 邮箱使用次数查询
━━━━━━━━━━━━━━━
👤 查询用户: ${targetUserId}
📧 剩余次数: ${result.remainingCount}
🎯 总签到天数: ${userData?.totalSignDays ?: 0}
📅 最后签到: ${userData?.lastSignDate ?: '未签到'}
👮 查询者: ${event.getUserIdStr()}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, """📧 邮箱使用次数查询
━━━━━━━━━━━━━━━
👤 查询用户: ${targetUserId}
❌ ${result.message}
💡 该用户需要先签到
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理查询其他用户邮箱次数失败", e)
            sendReply(event, "❌ 查询失败，请稍后再试")
        }
    }

    /**
     * 处理签到排行榜
     */
    private void handleSignRanking(QQEvent event) {
        try {
            Map result = signService.getSignRanking(10)

            if (result.success && result.rankings) {
                StringBuilder rankingText = new StringBuilder()
                rankingText.append("🏆 签到排行榜 TOP10\n")
                rankingText.append("━━━━━━━━━━━━━━━\n")

                result.rankings.eachWithIndex { userData, index ->
                    String medal = ""
                    switch (index) {
                        case 0: medal = "🥇"; break
                        case 1: medal = "🥈"; break
                        case 2: medal = "🥉"; break
                        default: medal = "${index + 1}."
                    }
                    
                    rankingText.append("${medal} ${userData.userId}\n")
                    rankingText.append("   📅 ${userData.totalSignDays}天 📧 ${userData.remainingEmailCount}次\n")
                }
                
                rankingText.append("━━━━━━━━━━━━━━━")
                sendReply(event, rankingText.toString())
            } else {
                sendReply(event, """🏆 签到排行榜
━━━━━━━━━━━━━━━
📭 暂无签到记录
💡 快来签到上榜吧！
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理签到排行榜失败", e)
            sendReply(event, "❌ 获取排行榜失败，请稍后再试")
        }
    }

    /**
     * 检查消息是否包含@用户和"添加邮箱"（添加邮箱次数格式）
     */
    private boolean hasAtAndAddEmail(List message) {
        boolean hasAt = false
        boolean hasAddEmail = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text") {
                    String text = segment.data?.text ?: ""
                    if (text.contains("添加邮箱")) {
                        hasAddEmail = true
                    }
                }
            }
        }

        return hasAt && hasAddEmail
    }

    /**
     * 检查消息是否包含@用户和"查询邮箱次数"（查询他人邮箱次数格式）
     */
    private boolean hasAtAndCheckEmail(List message) {
        boolean hasAt = false
        boolean hasCheckEmail = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text") {
                    String text = segment.data?.text ?: ""
                    if (text.contains("查询邮箱次数")) {
                        hasCheckEmail = true
                    }
                }
            }
        }

        return hasAt && hasCheckEmail
    }

    /**
     * 处理管理员添加邮箱次数
     */
    private void handleAddEmailCount(QQEvent event, String messageText) {
        try {
            // 检查是否为管理员
            if (!isAdmin(event)) {
                sendReply(event, "❌ 只有管理员可以使用此功能")
                return
            }

            // 从消息中提取被@的用户ID和次数
            String targetUserId = null
            int count = 0

            if (event.message instanceof List) {
                for (segment in event.message) {
                    if (segment instanceof Map && segment.type == "at") {
                        targetUserId = segment.data?.qq?.toString()
                    } else if (segment instanceof Map && segment.type == "text") {
                        String text = segment.data?.text ?: ""
                        // 提取数字作为次数
                        def matcher = text =~ /(\d+)/
                        if (matcher.find()) {
                            count = Integer.parseInt(matcher.group(1))
                        }
                    }
                }
            }

            if (!targetUserId) {
                sendReply(event, "❌ 请@要添加次数的用户")
                return
            }

            if (count <= 0) {
                sendReply(event, "❌ 次数必须大于0\n💡 正确格式：@用户 添加邮箱 次数")
                return
            }

            Map result = signService.addEmailCount(targetUserId, count)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """✅ 添加邮箱次数成功！
━━━━━━━━━━━━━━━
👤 目标用户: ${targetUserId}
➕ 添加次数: ${count}
📧 总次数: ${userData.remainingEmailCount}
👮 操作员: ${event.getUserIdStr()}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, "❌ 添加失败: ${result.message}")
            }

        } catch (NumberFormatException e) {
            sendReply(event, "❌ 次数必须是数字\n💡 正确格式：@用户 添加邮箱 次数")
        } catch (Exception e) {
            log.error("处理添加邮箱次数失败", e)
            sendReply(event, "❌ 添加失败，请稍后再试")
        }
    }

    /**
     * 处理生成CK
     */
    private void handleGenerateCk(QQEvent event) {
        try {
            String userId = event.getUserIdStr()

            // 检查是否为主人（主人可以无限制生成）
            if (isAdmin(event)) {
                generateCkForUser(event, userId, true)
                return
            }

            // 普通用户需要检查CK生成次数
            Map countResult = signService.checkCkCount(userId)
            if (!countResult.success || countResult.remainingCount <= 0) {
                sendReply(event, """❌ CK生成次数不足
━━━━━━━━━━━━━━━
🎫 剩余次数: ${countResult.remainingCount ?: 0}
💡 每签到2天获得1次CK生成机会
📅 发送"签到"开始积累天数
━━━━━━━━━━━━━━━""")
                return
            }

            generateCkForUser(event, userId, false)

        } catch (Exception e) {
            log.error("处理生成CK失败", e)
            sendReply(event, "❌ 生成CK失败，请稍后再试")
        }
    }

    /**
     * 为用户生成CK
     */
    private void generateCkForUser(QQEvent event, String userId, boolean isAdmin) {
        try {
            sendReply(event, "🔄 正在生成CK激活码，请稍候...")

            // 如果不是管理员，先消费次数
            if (!isAdmin) {
                Map consumeResult = signService.consumeCkCount(userId)
                if (!consumeResult.success) {
                    sendReply(event, "❌ ${consumeResult.message}")
                    return
                }
            }

            // 调用API生成CK
            String notes = isAdmin ? "主人生成" : "用户生成"
            Map ckResult = ckApiService.createCk(notes)

            if (ckResult.success) {
                String formattedCk = ckApiService.formatCkInfo(ckResult.data, userId)
                sendReply(event, formattedCk)

                log.info("CK生成成功: userId={}, isAdmin={}, ckCode={}",
                    userId, isAdmin, ckResult.data?.ck_code)
            } else {
                sendReply(event, "❌ CK生成失败: ${ckResult.error}")

                // 如果API调用失败，需要回退消费的次数
                if (!isAdmin) {
                    // 这里可以添加回退逻辑，暂时先记录日志
                    log.warn("CK生成失败，可能需要回退用户次数: userId={}", userId)
                }
            }

        } catch (Exception e) {
            log.error("生成CK失败: userId={}, isAdmin={}", userId, isAdmin, e)
            sendReply(event, "❌ 生成CK时出现错误，请稍后再试")
        }
    }

    /**
     * 处理查询CK次数
     */
    private void handleCheckCkCount(QQEvent event) {
        try {
            String userId = event.getUserIdStr()
            Map result = signService.checkCkCount(userId)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """🎫 CK生成次数查询
━━━━━━━━━━━━━━━
👤 用户: ${userId}
🎫 剩余次数: ${result.remainingCount}
📊 总生成数: ${userData?.totalCkGenerated ?: 0}
🎯 总签到天数: ${userData?.totalSignDays ?: 0}
📅 最后生成: ${userData?.lastCkGenDate ?: '未生成'}
💡 每签到2天获得1次CK生成机会
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, """🎫 CK生成次数查询
━━━━━━━━━━━━━━━
❌ ${result.message}
💡 发送"签到"开始积累天数
🎯 每签到2天获得1次CK生成机会
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理查询CK次数失败", e)
            sendReply(event, "❌ 查询失败，请稍后再试")
        }
    }

    /**
     * 检查消息是否包含@用户和"添加ck"
     */
    private boolean hasAtAndAddCk(Object message) {
        if (!(message instanceof List)) {
            return false
        }

        boolean hasAt = false
        boolean hasAddCk = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text" && segment.data?.text?.contains("添加ck")) {
                    hasAddCk = true
                }
            }
        }

        return hasAt && hasAddCk
    }

    /**
     * 检查消息是否包含@用户和"查询ck次数"
     */
    private boolean hasAtAndCheckCk(Object message) {
        if (!(message instanceof List)) {
            return false
        }

        boolean hasAt = false
        boolean hasCheckCk = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text" && segment.data?.text?.contains("查询ck次数")) {
                    hasCheckCk = true
                }
            }
        }

        return hasAt && hasCheckCk
    }

    /**
     * 处理添加CK次数
     */
    private void handleAddCkCount(QQEvent event, String messageText) {
        // 只在群聊中处理@用户命令
        if (!event.isGroupMessage()) {
            return
        }

        // 检查是否为管理员
        if (!isAdmin(event)) {
            sendReply(event, "❌ 只有管理员可以添加CK次数")
            return
        }

        try {
            // 提取@的用户ID和次数
            String targetUserId = extractAtUserId(event.message)
            if (!targetUserId) {
                sendReply(event, "❌ 未找到@的用户\n💡 正确格式：@用户 添加ck 次数")
                return
            }

            // 提取次数
            String countStr = messageText.replaceAll(".*添加ck\\s*", "").trim()
            if (countStr.isEmpty()) {
                sendReply(event, "❌ 请指定添加的次数\n💡 正确格式：@用户 添加ck 次数")
                return
            }

            int count = Integer.parseInt(countStr)
            if (count <= 0) {
                sendReply(event, "❌ 次数必须大于0")
                return
            }

            Map result = signService.addCkCount(targetUserId, count)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """✅ 添加CK次数成功！
━━━━━━━━━━━━━━━
👤 目标用户: ${targetUserId}
➕ 添加次数: ${count}
🎫 总次数: ${userData.remainingCkCount}
👮 操作员: ${event.getUserIdStr()}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, "❌ 添加失败: ${result.message}")
            }

        } catch (NumberFormatException e) {
            sendReply(event, "❌ 次数必须是数字\n💡 正确格式：@用户 添加ck 次数")
        } catch (Exception e) {
            log.error("处理添加CK次数失败", e)
            sendReply(event, "❌ 添加失败，请稍后再试")
        }
    }

    /**
     * 处理查询其他用户CK次数
     */
    private void handleCheckOtherCkCount(QQEvent event, String messageText) {
        // 只在群聊中处理@用户命令
        if (!event.isGroupMessage()) {
            return
        }

        // 检查是否为管理员
        if (!isAdmin(event)) {
            sendReply(event, "❌ 只有管理员可以查询其他用户CK次数")
            return
        }

        try {
            // 提取@的用户ID
            String targetUserId = extractAtUserId(event.message)
            if (!targetUserId) {
                sendReply(event, "❌ 未找到@的用户\n💡 正确格式：@用户 查询ck次数")
                return
            }

            Map result = signService.checkCkCount(targetUserId)

            if (result.success) {
                Map userData = result.data
                sendReply(event, """🎫 CK生成次数查询
━━━━━━━━━━━━━━━
👤 查询用户: ${targetUserId}
🎫 剩余次数: ${result.remainingCount}
📊 总生成数: ${userData?.totalCkGenerated ?: 0}
🎯 总签到天数: ${userData?.totalSignDays ?: 0}
📅 最后生成: ${userData?.lastCkGenDate ?: '未生成'}
👮 查询者: ${event.getUserIdStr()}
━━━━━━━━━━━━━━━""")
            } else {
                sendReply(event, """🎫 CK生成次数查询
━━━━━━━━━━━━━━━
👤 查询用户: ${targetUserId}
❌ ${result.message}
💡 该用户需要先签到
━━━━━━━━━━━━━━━""")
            }

        } catch (Exception e) {
            log.error("处理查询其他用户CK次数失败", e)
            sendReply(event, "❌ 查询失败，请稍后再试")
        }
    }

    /**
     * 检查是否为管理员
     */
    private boolean isAdmin(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
