package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * 禁言功能处理器
 */
@Component
@Slf4j
class BanHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Value('${bot.admin-qq}')
    private String adminQq

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        return event.message instanceof List && hasAtAndNumber(event.message)
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText()
        String userId = event.getUserIdStr()
        
        log.info("收到禁言请求: userId={}, message={}", userId, messageText)

        handleBanCommand(event, messageText)
    }

    @Override
    int getPriority() {
        return 8 // 高优先级
    }

    /**
     * 检查消息是否包含 @ 和数字（禁言格式，排除其他命令）
     */
    private boolean hasAtAndNumber(Object message) {
        if (!(message instanceof List)) {
            return false
        }

        boolean hasAt = false
        boolean hasNumber = false
        boolean hasExcludedKeywords = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text") {
                    String text = segment.data?.text ?: ""
                    // 检查是否包含数字
                    if (text.matches(/.*\d+.*/)) {
                        hasNumber = true
                    }
                    // 检查是否包含其他命令的关键词
                    if (text.contains("添加邮箱") || text.contains("查询邮箱次数") ||
                        text.contains("添加ck") || text.contains("查询ck次数") || text.contains("踢")) {
                        hasExcludedKeywords = true
                    }
                }
            }
        }

        // 只有包含@和数字，但不包含其他命令关键词时才匹配
        return hasAt && hasNumber && !hasExcludedKeywords
    }

    /**
     * 处理禁言命令
     */
    private void handleBanCommand(QQEvent event, String messageText) {
        // 只在群聊中处理禁言命令
        if (!event.isGroupMessage()) {
            return
        }

        // 检查是否为管理员
        if (!isAdmin(event)) {
            sendReply(event, "❌ 只有管理员可以使用禁言功能")
            return
        }

        try {
            // 从消息中提取被@的用户ID和禁言时长
            String targetUserId = null
            int minutes = 0

            if (event.message instanceof List) {
                for (segment in event.message) {
                    if (segment instanceof Map && segment.type == "at") {
                        targetUserId = segment.data?.qq?.toString()
                    } else if (segment instanceof Map && segment.type == "text") {
                        String text = segment.data?.text ?: ""
                        def matcher = text =~ /(\d+)/
                        if (matcher.find()) {
                            minutes = Integer.parseInt(matcher.group(1))
                        }
                    }
                }
            }

            if (!targetUserId) {
                sendReply(event, "❌ 请@要禁言的用户")
                return
            }

            if (minutes <= 0) {
                sendReply(event, "❌ 禁言时长必须大于0分钟")
                return
            }

            int seconds = minutes * 60
            String groupId = event.getGroupIdStr()

            log.info("管理员 {} 在群 {} 禁言用户 {} {} 分钟",
                    event.getUserIdStr(), groupId, targetUserId, minutes)

            // 调用禁言 API
            def result = apiService.banGroupMember(groupId, targetUserId, seconds)

            if (result.isSuccess()) {
                // 发送禁言成功消息
                List banSuccessMessage = [
                    [type: "text", data: [text: "🔇 禁言成功！\n━━━━━━━━━━━━━━━\n👤 用户："]],
                    [type: "at", data: [qq: Long.parseLong(targetUserId)]],
                    [type: "text", data: [text: "\n⏰ 时长：${minutes} 分钟\n👮 执行者："]],
                    [type: "at", data: [qq: Long.parseLong(event.getUserIdStr())]],
                    [type: "text", data: [text: "\n━━━━━━━━━━━━━━━"]]
                ]
                apiService.sendGroupMessageWithFormat(groupId, banSuccessMessage)
            } else {
                sendReply(event, "❌ 禁言失败：${result.getErrorMessage()}")
            }

        } catch (Exception e) {
            log.error("处理禁言命令失败", e)
            sendReply(event, "❌ 禁言命令处理失败：${e.message}")
        }
    }

    /**
     * 检查是否为管理员
     */
    private boolean isAdmin(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
