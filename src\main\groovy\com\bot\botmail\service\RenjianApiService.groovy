package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * 我在人间凑数的日子 API 服务
 */
@Service
@Slf4j
class RenjianApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取随机人间凑数句子
     */
    Map getRenjianQuote() {
        Map result = xxApiService.getRenjianQuote()
        if (result.success) {
            return [
                success: true,
                quote: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }

    /**
     * 格式化人间凑数句子
     */
    String formatRenjianQuote(String quote) {
        return xxApiService.formatRenjianQuote(quote)
    }
}
