package com.bot.botmail.entity

import groovy.transform.ToString

/**
 * QQ事件统一接收实体
 */
@ToString
class QQEvent {
    
    // 基础事件信息
    String post_type        // 事件类型: message, notice, request, meta_event
    Long time              // 事件发生的时间戳
    Long self_id           // 收到事件的机器人 QQ 号
    
    // 消息事件字段
    String message_type     // 消息类型: private, group
    String sub_type        // 消息子类型
    Long message_id        // 消息 ID
    Long user_id           // 发送者 QQ 号
    Object message         // 消息内容
    String raw_message     // 原始消息内容
    Integer font           // 字体
    
    // 群消息特有字段
    Long group_id          // 群号
    String anonymous       // 匿名信息
    
    // 发送者信息
    Map sender             // 发送者信息
    
    // 通知事件字段
    String notice_type     // 通知类型
    Long target_id         // 目标用户ID（如群成员增加事件中的新成员）

    // 请求事件字段
    String request_type    // 请求类型
    String comment         // 验证信息
    String flag           // 请求 flag
    
    /**
     * 判断是否为消息事件
     */
    boolean isMessageEvent() {
        return post_type == "message"
    }
    
    /**
     * 判断是否为私聊消息
     */
    boolean isPrivateMessage() {
        return isMessageEvent() && message_type == "private"
    }
    
    /**
     * 判断是否为群消息
     */
    boolean isGroupMessage() {
        return isMessageEvent() && message_type == "group"
    }
    
    /**
     * 获取消息文本内容
     */
    String getMessageText() {
        if (raw_message) {
            return raw_message.trim()
        }
        
        if (message instanceof String) {
            return message.toString().trim()
        }
        
        if (message instanceof List) {
            StringBuilder text = new StringBuilder()
            message.each { segment ->
                if (segment instanceof Map && segment.type == "text") {
                    text.append(segment.data?.text ?: "")
                }
            }
            return text.toString().trim()
        }
        
        return ""
    }
    
    /**
     * 获取发送者昵称
     */
    String getSenderNickname() {
        return sender?.nickname ?: sender?.card ?: "未知用户"
    }
    
    /**
     * 获取发送者QQ号字符串
     */
    String getUserIdStr() {
        return user_id?.toString()
    }
    
    /**
     * 获取群号字符串
     */
    String getGroupIdStr() {
        return group_id?.toString()
    }
}
