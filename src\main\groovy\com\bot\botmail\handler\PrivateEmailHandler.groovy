package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.GoMailApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture
import java.util.regex.Pattern

/**
 * 私人邮箱处理器（仅限机器人主人）
 */
@Component
@Slf4j
class PrivateEmailHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private GoMailApiService goMailService

    @Value('${bot.admin-qq}')
    private String adminQq

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        return messageText == "获取私人邮箱"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        
        // 检查是否为机器人主人
        if (!isOwner(event)) {
            sendReply(event, "❌ 此功能仅限机器人主人使用")
            return
        }

        log.info("收到获取私人邮箱请求: userId={}", userId)

        sendReply(event, "🔐 正在创建私人邮箱，请稍候...")

        CompletableFuture.runAsync {
            try {
                // 1. 创建私人邮箱
                Map mailboxResult = goMailService.createPrivateMailbox()
                if (!mailboxResult.success) {
                    sendReply(event, "❌ 创建私人邮箱失败: ${mailboxResult.error}")
                    return
                }

                String email = mailboxResult.data.email
                String expiresAt = mailboxResult.data.expiresAt

                sendReply(event, """🔐 私人邮箱创建成功！
━━━━━━━━━━━━━━━
📮 邮箱地址: ${email}
🏷️ 类型: 私人专用邮箱
⏰ 有效期: 24小时
🔍 开始监控邮件... (60秒内持续检查)
━━━━━━━━━━━━━━━""")

                // 2. 在60秒内不断检查邮件
                long startTime = System.currentTimeMillis()
                long timeout = 60000 // 60秒
                boolean found = false
                int checkCount = 0

                while (System.currentTimeMillis() - startTime < timeout && !found) {
                    checkCount++

                    // 每3秒检查一次
                    if (checkCount > 1) {
                        Thread.sleep(3000)
                    }

                    log.info("第 {} 次检查私人邮件: {}", checkCount, email)

                    // 3. 获取邮件列表
                    Map emailsResult = goMailService.getEmails(email)
                    if (!emailsResult.success) {
                        log.warn("获取私人邮件失败: {}", emailsResult.error)
                        continue
                    }

                    List emails = emailsResult.data?.emails
                    if (!emails || emails.isEmpty()) {
                        log.info("第 {} 次检查: 暂无私人邮件", checkCount)
                        continue
                    }

                    // 4. 获取第一封邮件详情
                    String firstEmailId = emails[0].id
                    Map emailDetail = goMailService.getEmailDetail(firstEmailId)

                    if (!emailDetail.success) {
                        log.warn("获取私人邮件详情失败: {}", emailDetail.error)
                        continue
                    }

                    // 5. 提取验证码
                    String subject = emailDetail.data?.subject ?: ""
                    String textContent = emailDetail.data?.textContent ?: ""
                    String htmlContent = emailDetail.data?.htmlContent ?: ""

                    String verificationCode = extractVerificationCode(subject, textContent, htmlContent)

                    if (verificationCode) {
                        found = true
                        long elapsedTime = (System.currentTimeMillis() - startTime) / 1000
                        sendReply(event, """✅ 私人邮箱验证码获取成功！
━━━━━━━━━━━━━━━
📧 邮箱: ${email}
📨 主题: ${subject}
🔑 验证码: ${verificationCode}
⏱️ 用时: ${elapsedTime}秒
🔐 私人专用
━━━━━━━━━━━━━━━""")
                    } else {
                        log.info("第 {} 次检查: 收到私人邮件但未找到验证码，主题: {}", checkCount, subject)
                        // 静默继续检查，不发送消息
                    }
                }

                // 如果60秒内没有找到验证码
                if (!found) {
                    sendReply(event, """⏰ 私人邮箱监控超时 (60秒)
━━━━━━━━━━━━━━━
📧 邮箱: ${email}
📭 结果: 未收到包含验证码的邮件
💡 建议: 重新获取私人邮箱或稍后再试
🔐 私人专用
━━━━━━━━━━━━━━━""")
                }

            } catch (Exception e) {
                log.error("处理获取私人邮箱命令失败", e)
                sendReply(event, "❌ 获取私人邮箱功能暂时不可用，请稍后再试")
            }
        }
    }

    @Override
    int getPriority() {
        return 7 // 高优先级，比普通邮箱稍高
    }

    /**
     * 检查是否为机器人主人
     */
    private boolean isOwner(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 提取验证码
     */
    private String extractVerificationCode(String subject, String textContent, String htmlContent) {
        // 常见验证码模式
        List<Pattern> patterns = [
            Pattern.compile("验证码[：:]?\\s*([0-9]{4,8})"),           // 验证码: 123456
            Pattern.compile("verification code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("([0-9]{6})"),                           // 6位数字
            Pattern.compile("([0-9]{4})"),                           // 4位数字
        ]

        String allContent = "${subject} ${textContent} ${htmlContent}"

        for (Pattern pattern : patterns) {
            def matcher = pattern.matcher(allContent)
            if (matcher.find()) {
                return matcher.group(1)
            }
        }

        return null
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
