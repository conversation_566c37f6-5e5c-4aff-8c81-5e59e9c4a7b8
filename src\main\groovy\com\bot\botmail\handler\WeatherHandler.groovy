package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.WeatherApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 天气 事件处理器
 */
@Component
@Slf4j
class WeatherHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private WeatherApiService weatherService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()

        // 处理命令：***天气
        return messageText.endsWith("天气")
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()
        
        log.info("收到天气查询请求: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleWeatherRequest(event, messageText)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理天气查询请求
     */
    private void handleWeatherRequest(QQEvent event, String messageText) {
        try {
            // 提取城市名称
            String cityName = extractCityName(messageText)
            
            sendReply(event, "🌤️ 正在为你查询${cityName ? cityName : ''}天气信息...")

            // 调用API获取天气信息
            Map result = weatherService.getWeatherInfo(cityName)

            if (result.success) {
                Map weatherData = result.weatherData
                String formattedWeather = weatherService.formatWeatherInfo(weatherData)

                sendReply(event, formattedWeather)

                log.info("天气信息发送成功: userId={}, city={}", event.getUserIdStr(), weatherData?.city)
            } else {
                sendReply(event, "❌ 获取天气信息失败: ${result.error}")
                log.error("获取天气信息失败: {}", result.error)
            }

        } catch (Exception e) {
            log.error("处理天气查询请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 提取城市名称
     */
    private String extractCityName(String messageText) {
        // 去掉"天气"后缀
        if (messageText.endsWith("天气")) {
            return messageText.substring(0, messageText.length() - 2).trim()
        }
        return messageText
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
