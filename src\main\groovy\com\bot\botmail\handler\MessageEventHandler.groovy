package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * 消息事件处理器 - 只处理帮助命令和普通消息
 */
@Component
@Slf4j
class MessageEventHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Value('${bot.admin-qq}')
    private String adminQq

    // 应用启动时间
    private static final long START_TIME = System.currentTimeMillis()

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 只处理帮助命令，其他命令由专门的处理器处理
        return messageText == "帮助"
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText().trim()
        String userId = event.getUserIdStr()
        
        log.info("收到消息: userId={}, message={}", userId, messageText)

        // 处理命令
        if (messageText == "帮助") {
            handleHelpCommand(event)
        } else {
            handleNormalMessage(event, messageText)
        }
    }

    @Override
    int getPriority() {
        return 1 // 最低优先级，作为兜底处理器
    }

    /**
     * 处理帮助命令
     */
    private void handleHelpCommand(QQEvent event) {
        boolean isAdminUser = isAdmin(event)
        boolean isOwnerUser = isOwner(event)
        
        String adminCommands = isAdminUser ? """

┌─ 👑 管理员专属 ────────────────┐
│ 🔇 @用户 数字 - 禁言用户（分钟）   │
│    💡 例如：@某人 10            │
│ 👢 @用户 踢 - 踢出群成员          │
│    💡 例如：@某人 踢             │
│ 📧 @用户 添加邮箱 次数           │
│    💡 例如：@某人 添加邮箱 10     │
│ 📊 @用户 查询邮箱次数            │
│    💡 例如：@某人 查询邮箱次数     │
└──────────────────────────────┘""" : ""

        String ownerCommands = isOwnerUser ? """

┌─ 🔐 主人专属 ──────────────────┐
│ 📧 获取私人邮箱 - 创建私人专用邮箱  │
│    💡 域名：159email.shop       │
│ 🎯 创建邮箱 前缀@域名 - 任意邮箱   │
│    💡 例如：创建邮箱 <EMAIL> │
└──────────────────────────────┘""" : ""

        String helpText = """╭──────────────────────────────╮
│         🤖 呆橘QQ机器人         │
╰──────────────────────────────╯

┌─ 📋 基础功能 ──────────────────┐
│ 🆘 帮助 - 显示此帮助信息          │
│ 📅 签到 - 每日签到获取邮箱次数     │
│ 📧 获取邮箱 - 创建临时邮箱并获取验证码│
│ 📊 查询邮箱次数 - 查看剩余使用次数  │
│ 🏆 签到排行榜 - 查看签到排行      │
│ 🚫 过期邮箱 - 临时邮箱(7月5日到期) │
│    💡 域名：@daiju.cyou 无限制使用 │
│ 🎯 过期邮箱 前缀 - 自定义前缀邮箱   │
│    💡 例如：过期邮箱 myname123    │
│ ✅ 审核 用户ID - 审核通过用户      │
│    💡 例如：审核 123            │
│ 🤖 问 问题内容 - AI智能对话       │
│    💡 例如：问 今天天气怎么样？     │
└──────────────────────────────┘

┌─ ⭐ 项目相关 ──────────────────┐
│ 🌟 看星星 - 查看项目 Stars 数量   │
│ 📊 项目信息 - 查看完整项目信息      │
└──────────────────────────────┘

┌─ 🎭 娱乐功能 ──────────────────┐
│ 📖 我在人间凑数的日子 - 获取人间感悟│
│ 💔 我要当舔狗 - 获取舔狗日记       │
│ 🖼️ 你也好这口 - 随机JK图片        │
│ 🖤 嗨丝 - 随机黑丝图片           │
│ 🤍 露丝 - 随机白丝图片           │
│ 🎬 给爷舞个 - 随机美女视频        │
└──────────────────────────────┘

┌─ 🔍 查询功能 ──────────────────┐
│ 🌤️ ***天气 - 查询天气信息        │
│    💡 例如：北京天气             │
│ ⚖️ 看看自己几斤几两 - 查询自己的QQ信息│
└──────────────────────────────┘${ownerCommands}${adminCommands}

╭──────────────────────────────╮
│ 🔧 状态：运行正常               │
│ ⏰ 运行时间：${formatRunningTime()} │
╰──────────────────────────────╯"""

        sendReply(event, helpText)
    }

    /**
     * 格式化运行时间
     */
    private String formatRunningTime() {
        long currentTime = System.currentTimeMillis()
        long runningTime = currentTime - START_TIME
        
        long seconds = runningTime / 1000
        long minutes = seconds / 60
        long hours = minutes / 60
        long days = hours / 24
        
        seconds %= 60
        minutes %= 60
        hours %= 24
        
        return "${days}天${hours}时${minutes}分${seconds}秒"
    }

    /**
     * 处理普通消息
     */
    private void handleNormalMessage(QQEvent event, String messageText) {
        // 这里可以添加其他普通消息的处理逻辑
        // 目前暂时不处理
    }

    /**
     * 检查是否为管理员
     */
    private boolean isAdmin(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 检查是否为机器人主人
     */
    private boolean isOwner(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq // 目前主人和管理员是同一人
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
