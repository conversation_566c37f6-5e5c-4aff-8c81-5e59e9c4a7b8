package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.DeepSeekApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture
import java.util.concurrent.ConcurrentHashMap

/**
 * AI 对话事件处理器
 */
@Component
@Slf4j
class AIConversationHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private DeepSeekApiService deepSeekService

    // 存储用户对话上下文 (用户ID -> 对话历史)
    private Map<String, List<Map>> conversationContext = new ConcurrentHashMap<>()

    // 上下文保持时间 (30分钟)
    private static final long CONTEXT_TIMEOUT = 30 * 60 * 1000L

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText()
        log.info("AIConversationHandler 检查消息: '{}'", messageText)

        // 处理格式：问 问题内容
        boolean canHandle = messageText.startsWith("问 ")
        log.info("AIConversationHandler canHandle: {}", canHandle)
        return canHandle
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText()
        String userId = event.getUserIdStr()
        
        log.info("收到AI对话请求: userId={}, message={}", userId, messageText)

        // 提取用户问题
        String userQuestion = extractQuestion(messageText)
        if (userQuestion.isEmpty()) {
            sendReply(event, "❓ 请输入您的问题\n例如：问 今天天气怎么样？")
            return
        }

        // 异步处理AI对话，避免阻塞
        CompletableFuture.runAsync {
            handleAIConversation(event, userId, userQuestion)
        }
    }

    @Override
    int getPriority() {
        return 5 // 高优先级，在 MessageEventHandler 之前处理
    }

    /**
     * 处理AI对话
     */
    private void handleAIConversation(QQEvent event, String userId, String question) {
        try {
            // 检查API配置
            if (!deepSeekService.isConfigValid()) {
                sendReply(event, "❌ AI服务暂时不可用，请稍后再试")
                return
            }

            sendReply(event, "🤖 正在思考中...")

            // 获取用户对话上下文
            List<Map> context = getUserContext(userId)
            
            // 调用AI API
            Map result = deepSeekService.chatWithContext(context, question)
            
            if (result.success) {
                String reply = result.reply
                
                // 更新对话上下文
                updateUserContext(userId, question, reply)
                
                // 发送AI回复
                sendReply(event, "🤖 ${reply}")
                
                log.info("AI对话成功: userId={}, question={}, reply={}", 
                        userId, question, reply.take(50))
            } else {
                sendReply(event, "❌ AI服务出现问题: ${result.error}")
                log.error("AI对话失败: userId={}, error={}", userId, result.error)
            }

        } catch (Exception e) {
            log.error("处理AI对话时发生异常", e)
            sendReply(event, "❌ 处理您的问题时出现了错误，请稍后再试")
        }
    }

    /**
     * 提取用户问题
     */
    private String extractQuestion(String messageText) {
        String text = messageText.trim()

        // 移除"问 "前缀
        if (text.startsWith("问 ")) {
            return text.substring(2).trim()
        }

        return text
    }

    /**
     * 获取用户对话上下文
     */
    private List<Map> getUserContext(String userId) {
        List<Map> context = conversationContext.get(userId)
        if (context == null) {
            context = []
            conversationContext.put(userId, context)
        }
        
        // 清理过期的上下文
        cleanExpiredContext()
        
        return context
    }

    /**
     * 更新用户对话上下文
     */
    private void updateUserContext(String userId, String question, String reply) {
        List<Map> context = conversationContext.get(userId)
        if (context == null) {
            context = []
            conversationContext.put(userId, context)
        }
        
        // 添加用户问题和AI回复
        context.add([
            role: "user",
            content: question,
            timestamp: System.currentTimeMillis()
        ])
        context.add([
            role: "assistant", 
            content: reply,
            timestamp: System.currentTimeMillis()
        ])
        
        // 限制上下文长度，保留最近的10轮对话
        if (context.size() > 20) {
            context = context.subList(context.size() - 20, context.size())
            conversationContext.put(userId, context)
        }
    }

    /**
     * 清理过期的对话上下文
     */
    private void cleanExpiredContext() {
        long currentTime = System.currentTimeMillis()
        
        conversationContext.entrySet().removeIf { entry ->
            List<Map> context = entry.value
            if (context.isEmpty()) {
                return true
            }
            
            // 检查最后一条消息的时间
            Map lastMessage = context.last()
            long lastTimestamp = lastMessage.timestamp as Long
            
            return (currentTime - lastTimestamp) > CONTEXT_TIMEOUT
        }
    }

    /**
     * 清除指定用户的对话上下文
     */
    void clearUserContext(String userId) {
        conversationContext.remove(userId)
        log.info("清除用户对话上下文: userId={}", userId)
    }

    /**
     * 获取当前活跃对话数量
     */
    int getActiveConversationCount() {
        cleanExpiredContext()
        return conversationContext.size()
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
