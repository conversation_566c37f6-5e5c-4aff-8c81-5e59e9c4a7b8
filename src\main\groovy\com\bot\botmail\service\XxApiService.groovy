package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Value
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * XXAPI 统一服务
 */
@Service
@Slf4j
class XxApiService {

    @Value('${xxapi.base-url}')
    private String baseUrl

    private RestTemplate restTemplate = new RestTemplate()

    /**
     * 统一调用XXAPI接口
     */
    Map callXxApi(String endpoint) {
        return callXxApiWithParams(endpoint, [:])
    }

    /**
     * 统一调用XXAPI接口（带参数）
     */
    Map callXxApiWithParams(String endpoint, Map<String, String> params) {
        try {
            log.info("调用XXAPI: {}, 参数: {}", endpoint, params)

            String fullUrl = "${baseUrl}/${endpoint}"

            // 添加查询参数
            if (params && !params.isEmpty()) {
                List<String> paramList = []
                params.each { key, value ->
                    paramList.add("${key}=${URLEncoder.encode(value, 'UTF-8')}")
                }
                fullUrl += "?" + paramList.join("&")
            }

            HttpHeaders headers = new HttpHeaders()
            headers.set("User-Agent", "QQ-Bot")

            HttpEntity request = new HttpEntity<>(headers)

            ResponseEntity<Map> response = restTemplate.exchange(
                fullUrl,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = response.getBody()
            log.info("XXAPI响应: endpoint={}, code={}", endpoint, result?.code)

            if (result?.code == 200) {
                return [
                    success: true,
                    data: result.data,
                    message: result.msg
                ]
            } else {
                return [
                    success: false,
                    error: "API返回错误: ${result?.msg ?: '未知错误'}"
                ]
            }

        } catch (Exception e) {
            log.error("调用XXAPI失败: endpoint={}, params={}", endpoint, params, e)
            return [
                success: false,
                error: "API调用失败: ${e.message}"
            ]
        }
    }

    /**
     * 获取人间凑数句子
     */
    Map getRenjianQuote() {
        return callXxApi("renjian")
    }

    /**
     * 获取舔狗日记
     */
    Map getDogDiary() {
        return callXxApi("dog")
    }

    /**
     * 获取JK图片
     */
    Map getJkImage() {
        return callXxApi("jk")
    }

    /**
     * 获取美女视频
     */
    Map getMeinvVideo() {
        return callXxApi("meinv")
    }

    /**
     * 获取黑丝图片
     */
    Map getHeisiImage() {
        return callXxApi("heisi")
    }

    /**
     * 获取白丝图片
     */
    Map getBaisiImage() {
        return callXxApi("baisi")
    }

    /**
     * 获取天气信息
     */
    Map getWeatherInfo(String city = null) {
        if (city) {
            return callXxApiWithParams("weather", [city: city])
        } else {
            return callXxApi("weather")
        }
    }

    /**
     * 格式化人间凑数句子
     */
    String formatRenjianQuote(String quote) {
        if (!quote) {
            return "❌ 无法获取人间凑数句子"
        }

        return """📖 我在人间凑数的日子
━━━━━━━━━━━━━━━
💭 ${quote}
━━━━━━━━━━━━━━━"""
    }

    /**
     * 格式化舔狗日记
     */
    String formatDogDiary(String diary) {
        if (!diary) {
            return "❌ 无法获取舔狗日记"
        }

        return """🐕 舔狗日记
━━━━━━━━━━━━━━━
💔 ${diary}
━━━━━━━━━━━━━━━"""
    }
}
