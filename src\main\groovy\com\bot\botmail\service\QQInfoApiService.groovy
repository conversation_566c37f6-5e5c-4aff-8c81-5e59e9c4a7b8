package com.bot.botmail.service


import groovy.util.logging.Slf4j
import org.springframework.http.HttpEntity
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Service
import org.springframework.web.client.RestTemplate

/**
 * QQ信息查询 API 服务
 */
@Service
@Slf4j
class QQInfoApiService {

    private RestTemplate restTemplate = new RestTemplate()
    private static final String API_URL = "https://jkapi.com/api/qqinfo"

    /**
     * 获取QQ信息
     */
    Map getQQInfo(String qq) {
        try {
            log.info("获取QQ信息: qq={}", qq)

            String fullUrl = "${API_URL}?qq=${qq}"
            
            HttpHeaders headers = new HttpHeaders()
            headers.set("User-Agent", "QQ-Bot")
            
            HttpEntity request = new HttpEntity<>(headers)
            
            // 直接使用RestTemplate获取Map
            ResponseEntity<Map> mapResponse = restTemplate.exchange(
                fullUrl,
                HttpMethod.GET,
                request,
                Map.class
            )

            Map result = mapResponse.getBody()
            log.info("QQ信息API响应成功: qq={}, result={}", qq, result)

            if (result) {
                // 检查API是否成功
                if (result.code == 200) {
                    return [
                        success: true,
                        data: result
                    ]
                } else {
                    return [
                        success: false,
                        error: result.msg ?: "API返回错误"
                    ]
                }
            } else {
                return [
                    success: false,
                    error: "API响应为空"
                ]
            }

        } catch (Exception e) {
            log.error("获取QQ信息失败: qq={}", qq, e)
            return [
                success: false,
                error: "获取QQ信息失败: ${e.message}"
            ]
        }
    }

    /**
     * 格式化QQ信息
     */
    String formatQQInfo(Map qqInfo, String qq) {
        if (!qqInfo) {
            return "❌ 无法获取QQ信息"
        }

        String nickname = qqInfo.nick ?: "未知"
        String qqLevel = qqInfo.level?.toString() ?: "0"
        String regYear = qqInfo.regYear ?: "未知"
        String isVip = qqInfo.is_vip ? "是" : "否"
        String isYearsVip = qqInfo.is_years_vip ? "是" : "否"
        String vipLevel = qqInfo.vip_level?.toString() ?: "0"
        String avatar = qqInfo.avatar ?: ""
        String email = qqInfo.email ?: ""

        return """⚖️ 看看你有几斤几两
━━━━━━━━━━━━━━━
🆔 QQ号: ${qq}
📝 昵称: ${nickname}
⭐ QQ等级: ${qqLevel}
📅 注册年份: ${regYear}年
👑 是否VIP: ${isVip}
💎 VIP等级: ${vipLevel}
🎖️ 年费VIP: ${isYearsVip}
📧 邮箱: ${email}
🖼️ 头像: ${avatar}
━━━━━━━━━━━━━━━"""
    }
}
