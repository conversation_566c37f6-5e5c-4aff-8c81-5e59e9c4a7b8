package com.bot.botmail.service

import com.fasterxml.jackson.databind.ObjectMapper
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.LocalDate
import java.time.format.DateTimeFormatter

/**
 * 用户签到服务
 */
@Service
@Slf4j
class UserSignService {

    private static final String DATA_DIR = "data"
    private static final String SIGN_DATA_FILE = "user_sign_data.json"
    private static final int DAILY_REWARD = 5 // 每日签到奖励次数
    private static final int CK_REWARD_DAYS = 2 // 每签到2天获得1次CK生成机会

    @Autowired
    private ObjectMapper objectMapper

    private DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")

    /**
     * 用户签到
     */
    Map userSignIn(String userId) {
        try {
            Map signData = loadSignData()
            String today = LocalDate.now().format(dateFormatter)
            
            Map userRecord = signData[userId] ?: [
                userId: userId,
                lastSignDate: null,
                remainingEmailCount: 0,
                totalSignDays: 0,
                createTime: today,
                remainingCkCount: 0,        // CK生成剩余次数
                totalCkGenerated: 0,        // 总共生成的CK数量
                lastCkGenDate: null         // 最后生成CK的日期
            ]

            // 检查今日是否已签到
            if (userRecord.lastSignDate == today) {
                return [
                    success: false,
                    message: "今日已签到",
                    data: userRecord
                ]
            }

            // 执行签到
            userRecord.lastSignDate = today
            userRecord.remainingEmailCount = (userRecord.remainingEmailCount ?: 0) + DAILY_REWARD
            userRecord.totalSignDays = (userRecord.totalSignDays ?: 0) + 1

            // 检查是否获得CK生成机会（每签到2天获得1次）
            int totalDays = userRecord.totalSignDays
            if (totalDays % CK_REWARD_DAYS == 0) {
                userRecord.remainingCkCount = (userRecord.remainingCkCount ?: 0) + 1
                log.info("用户获得CK生成机会: userId={}, 总签到天数={}, CK剩余次数={}",
                    userId, totalDays, userRecord.remainingCkCount)
            }

            // 保存数据
            signData[userId] = userRecord
            saveSignData(signData)

            log.info("用户签到成功: userId={}, 总签到天数={}, 剩余次数={}", 
                userId, userRecord.totalSignDays, userRecord.remainingEmailCount)

            return [
                success: true,
                message: "签到成功",
                data: userRecord
            ]

        } catch (Exception e) {
            log.error("用户签到失败: userId={}", userId, e)
            return [
                success: false,
                message: "签到失败: ${e.message}"
            ]
        }
    }

    /**
     * 检查用户邮箱使用次数
     */
    Map checkEmailCount(String userId) {
        try {
            Map signData = loadSignData()
            Map userRecord = signData[userId]

            if (!userRecord) {
                return [
                    success: false,
                    message: "未找到签到记录，请先签到",
                    remainingCount: 0
                ]
            }

            int remainingCount = userRecord.remainingEmailCount ?: 0
            return [
                success: true,
                message: "查询成功",
                remainingCount: remainingCount,
                data: userRecord
            ]

        } catch (Exception e) {
            log.error("检查邮箱次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "查询失败: ${e.message}",
                remainingCount: 0
            ]
        }
    }

    /**
     * 消费邮箱使用次数
     */
    Map consumeEmailCount(String userId) {
        try {
            Map signData = loadSignData()
            Map userRecord = signData[userId]

            if (!userRecord) {
                return [
                    success: false,
                    message: "未找到签到记录，请先签到"
                ]
            }

            int remainingCount = userRecord.remainingEmailCount ?: 0
            if (remainingCount <= 0) {
                return [
                    success: false,
                    message: "邮箱使用次数不足，请签到获取"
                ]
            }

            // 消费一次
            userRecord.remainingEmailCount = remainingCount - 1
            signData[userId] = userRecord
            saveSignData(signData)

            log.info("消费邮箱次数: userId={}, 剩余次数={}", userId, userRecord.remainingEmailCount)

            return [
                success: true,
                message: "次数消费成功",
                remainingCount: userRecord.remainingEmailCount
            ]

        } catch (Exception e) {
            log.error("消费邮箱次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "消费失败: ${e.message}"
            ]
        }
    }

    /**
     * 管理员添加邮箱次数
     */
    Map addEmailCount(String userId, int count) {
        try {
            Map signData = loadSignData()
            String today = LocalDate.now().format(dateFormatter)
            
            Map userRecord = signData[userId] ?: [
                userId: userId,
                lastSignDate: null,
                remainingEmailCount: 0,
                totalSignDays: 0,
                createTime: today,
                remainingCkCount: 0,
                totalCkGenerated: 0,
                lastCkGenDate: null
            ]

            userRecord.remainingEmailCount = (userRecord.remainingEmailCount ?: 0) + count
            signData[userId] = userRecord
            saveSignData(signData)

            log.info("管理员添加邮箱次数: userId={}, 添加次数={}, 总次数={}", 
                userId, count, userRecord.remainingEmailCount)

            return [
                success: true,
                message: "添加成功",
                data: userRecord
            ]

        } catch (Exception e) {
            log.error("添加邮箱次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "添加失败: ${e.message}"
            ]
        }
    }

    /**
     * 获取签到排行榜
     */
    Map getSignRanking(int limit = 10) {
        try {
            Map signData = loadSignData()
            
            List<Map> rankings = signData.values()
                .findAll { it.totalSignDays > 0 }
                .sort { -it.totalSignDays }
                .take(limit)

            return [
                success: true,
                message: "获取排行榜成功",
                rankings: rankings,
                total: signData.size()
            ]

        } catch (Exception e) {
            log.error("获取签到排行榜失败", e)
            return [
                success: false,
                message: "获取排行榜失败: ${e.message}",
                rankings: []
            ]
        }
    }

    /**
     * 加载签到数据
     */
    private Map loadSignData() {
        try {
            Path dataDir = Paths.get(DATA_DIR)
            if (!Files.exists(dataDir)) {
                Files.createDirectories(dataDir)
            }

            Path dataFile = dataDir.resolve(SIGN_DATA_FILE)
            if (!Files.exists(dataFile)) {
                return [:]
            }

            String content = Files.readString(dataFile)
            return objectMapper.readValue(content, Map.class)

        } catch (Exception e) {
            log.warn("加载签到数据失败，返回空数据", e)
            return [:]
        }
    }

    /**
     * 保存签到数据
     */
    private void saveSignData(Map data) {
        try {
            Path dataDir = Paths.get(DATA_DIR)
            if (!Files.exists(dataDir)) {
                Files.createDirectories(dataDir)
            }

            Path dataFile = dataDir.resolve(SIGN_DATA_FILE)
            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(data)
            Files.writeString(dataFile, jsonContent)

        } catch (Exception e) {
            log.error("保存签到数据失败", e)
            throw e
        }
    }

    /**
     * 检查用户CK生成次数
     */
    Map checkCkCount(String userId) {
        try {
            Map signData = loadSignData()
            Map userRecord = signData[userId]

            if (!userRecord) {
                return [
                    success: false,
                    message: "未找到签到记录，请先签到",
                    remainingCount: 0
                ]
            }

            int remainingCount = userRecord.remainingCkCount ?: 0
            return [
                success: true,
                message: "查询成功",
                remainingCount: remainingCount,
                data: userRecord
            ]

        } catch (Exception e) {
            log.error("检查CK次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "查询失败: ${e.message}",
                remainingCount: 0
            ]
        }
    }

    /**
     * 消费CK生成次数
     */
    Map consumeCkCount(String userId) {
        try {
            Map signData = loadSignData()
            Map userRecord = signData[userId]

            if (!userRecord) {
                return [
                    success: false,
                    message: "未找到签到记录，请先签到"
                ]
            }

            int remainingCount = userRecord.remainingCkCount ?: 0
            if (remainingCount <= 0) {
                return [
                    success: false,
                    message: "CK生成次数不足，每签到2天获得1次机会"
                ]
            }

            // 消费一次
            String today = LocalDate.now().format(dateFormatter)
            userRecord.remainingCkCount = remainingCount - 1
            userRecord.totalCkGenerated = (userRecord.totalCkGenerated ?: 0) + 1
            userRecord.lastCkGenDate = today
            signData[userId] = userRecord
            saveSignData(signData)

            log.info("消费CK生成次数: userId={}, 剩余次数={}", userId, userRecord.remainingCkCount)

            return [
                success: true,
                message: "次数消费成功",
                remainingCount: userRecord.remainingCkCount
            ]

        } catch (Exception e) {
            log.error("消费CK次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "消费失败: ${e.message}"
            ]
        }
    }

    /**
     * 管理员添加CK生成次数
     */
    Map addCkCount(String userId, int count) {
        try {
            Map signData = loadSignData()
            String today = LocalDate.now().format(dateFormatter)

            Map userRecord = signData[userId] ?: [
                userId: userId,
                lastSignDate: null,
                remainingEmailCount: 0,
                totalSignDays: 0,
                createTime: today,
                remainingCkCount: 0,
                totalCkGenerated: 0,
                lastCkGenDate: null
            ]

            userRecord.remainingCkCount = (userRecord.remainingCkCount ?: 0) + count
            signData[userId] = userRecord
            saveSignData(signData)

            log.info("管理员添加CK次数: userId={}, 添加次数={}, 总次数={}",
                userId, count, userRecord.remainingCkCount)

            return [
                success: true,
                message: "添加成功",
                data: userRecord
            ]

        } catch (Exception e) {
            log.error("添加CK次数失败: userId={}", userId, e)
            return [
                success: false,
                message: "添加失败: ${e.message}"
            ]
        }
    }
}
