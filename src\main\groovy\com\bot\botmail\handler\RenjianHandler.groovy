package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.RenjianApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 我在人间凑数的日子 事件处理器
 */
@Component
@Slf4j
class RenjianHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private RenjianApiService renjianService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理命令：我在人间凑数的日子
        return messageText == "我在人间凑数的日子"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        
        log.info("收到人间凑数请求: userId={}", userId)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleRenjianRequest(event)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理人间凑数请求
     */
    private void handleRenjianRequest(QQEvent event) {
        try {
            sendReply(event, "📖 正在为你寻找人间感悟...")

            // 调用API获取句子
            Map result = renjianService.getRenjianQuote()
            
            if (result.success) {
                String formattedQuote = renjianService.formatRenjianQuote(result.quote)
                sendReply(event, formattedQuote)
                
                log.info("人间凑数句子发送成功: userId={}", event.getUserIdStr())
            } else {
                sendReply(event, "❌ 获取人间感悟失败: ${result.error}")
                log.error("获取人间凑数句子失败: {}", result.error)
            }

        } catch (Exception e) {
            log.error("处理人间凑数请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
