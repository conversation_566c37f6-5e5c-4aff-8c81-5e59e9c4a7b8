package com.bot.botmail.entity

import groovy.transform.ToString

/**
 * 统一的API响应实体
 */
@ToString
class ApiResponse {
    
    String status          // 状态: ok, failed
    Integer retcode        // 返回码
    Object data           // 响应数据
    String message        // 错误信息
    String echo           // 回声
    
    /**
     * 判断是否成功
     */
    boolean isSuccess() {
        return status == "ok" && (retcode == null || retcode == 0)
    }
    
    /**
     * 获取错误信息
     */
    String getErrorMessage() {
        return message ?: "未知错误"
    }
    
    /**
     * 创建成功响应
     */
    static ApiResponse success(Object data = null) {
        return new ApiResponse(
            status: "ok",
            retcode: 0,
            data: data
        )
    }
    
    /**
     * 创建失败响应
     */
    static ApiResponse failed(String message, Integer retcode = -1) {
        return new ApiResponse(
            status: "failed",
            retcode: retcode,
            message: message
        )
    }
}

/**
 * 控制器统一返回实体
 */
@ToString
class BotResponse {
    
    boolean success = true
    String message = "ok"
    Object data = null
    Long timestamp = System.currentTimeMillis()
    
    /**
     * 创建成功响应
     */
    static BotResponse ok(Object data = null, String message = "ok") {
        return new BotResponse(
            success: true,
            message: message,
            data: data
        )
    }
    
    /**
     * 创建失败响应
     */
    static BotResponse error(String message, Object data = null) {
        return new BotResponse(
            success: false,
            message: message,
            data: data
        )
    }
    
    /**
     * 转换为Map（用于JSON返回）
     */
    Map toMap() {
        return [
            success: success,
            message: message,
            data: data,
            timestamp: timestamp
        ]
    }
}
