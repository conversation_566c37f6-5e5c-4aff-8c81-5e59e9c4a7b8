package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.GitHubApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

/**
 * 通知事件处理器
 */
@Component
@Slf4j
class NoticeEventHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private GitHubApiService gitHubService

    @Override
    boolean canHandle(QQEvent event) {
        return event.post_type == "notice"
    }

    @Override
    void handle(QQEvent event) {
        log.info("收到通知事件: type={}, subType={}", event.notice_type, event.sub_type)

        switch (event.notice_type) {
            case "group_increase":
                handleGroupIncrease(event)
                break
            case "group_decrease":
                handleGroupDecrease(event)
                break
            default:
                log.debug("未处理的通知事件: {}", event.notice_type)
        }
    }

    @Override
    int getPriority() {
        return 20 // 通知事件优先级中等
    }

    /**
     * 处理群成员增加
     */
    private void handleGroupIncrease(QQEvent event) {
        String groupId = event.getGroupIdStr()

        // 在群成员增加事件中，新成员的ID在target_id字段，而不是user_id字段
        String userId = event.target_id?.toString()

        log.info("新成员加入群: groupId={}, userId={}, event.user_id={}, event.target_id={}",
                groupId, userId, event.user_id, event.target_id)

        // 获取 GitHub Stars 信息
        String starsInfo = getStarsInfo()

        // 创建包含 @ 的欢迎消息
        List welcomeMessage = [
            [type: "text", data: [text: "🎉 欢迎新成员！\n━━━━━━━━━━━━━━━\n👋 欢迎 "]],
            [type: "at", data: [qq: Long.parseLong(userId)]],
            [type: "text", data: [text: """ 加入本群！

📋 请查看群公告了解群规
💡 发送 帮助 查看机器人功能

🔗 实用链接：
📧 GoMail 邮箱: https://184772.xyz
🔑 API Token: https://augment.184772.xyz/
🔧 IDEA 无感换号插件: https://github.com/xn030523/augment-token-idea-free/releases
${starsInfo}
━━━━━━━━━━━━━━━"""]]
        ]

        // 发送消息
        apiService.sendGroupMessageWithFormat(groupId, welcomeMessage)
    }

    /**
     * 处理群成员减少
     */
    private void handleGroupDecrease(QQEvent event) {
        String groupId = event.getGroupIdStr()
        String userId = event.getUserIdStr()

        log.info("成员离开群: groupId={}, userId={}, subType={}", groupId, userId, event.sub_type)

        // 创建包含 @ 的离开消息
        List leaveMessage = [
            [type: "text", data: [text: "👋 成员 "]],
            [type: "at", data: [qq: Long.parseLong(userId)]],
            [type: "text", data: [text: " 离开了群聊"]]
        ]

        // 发送离开消息
        apiService.sendGroupMessageWithFormat(groupId, leaveMessage)
    }



    /**
     * 获取 GitHub Stars 信息
     */
    private String getStarsInfo() {
        try {
            Map result = gitHubService.getAugmentTokenStars()
            if (result.success) {
                return "\n⭐ IDEA 插件 Stars: ${result.stars} 个，求点赞！"
            } else {
                return "\n⭐ IDEA 插件求 Stars 支持！"
            }
        } catch (Exception e) {
            log.warn("获取 GitHub Stars 失败", e)
            return "\n⭐ IDEA 插件求 Stars 支持！"
        }
    }
}
