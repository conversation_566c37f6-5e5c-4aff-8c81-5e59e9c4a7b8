package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.BaisiImageApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 白丝图片 事件处理器
 */
@Component
@Slf4j
class BaisiImageHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private BaisiImageApiService baisiImageService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理命令：露丝
        return messageText == "露丝"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()
        
        log.info("收到白丝图片请求: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleBaisiImageRequest(event)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理白丝图片请求
     */
    private void handleBaisiImageRequest(QQEvent event) {
        try {
            sendReply(event, "🖼️ 正在为你获取随机白丝图片...")

            // 调用API获取图片URL
            Map result = baisiImageService.getRandomBaisiImage()
            
            if (result.success) {
                String imageUrl = result.imageUrl
                
                // 发送图片消息
                sendImageMessage(event, imageUrl)
                
                log.info("白丝图片发送成功: userId={}, imageUrl={}", event.getUserIdStr(), imageUrl)
            } else {
                sendReply(event, "❌ 获取白丝图片失败: ${result.error}")
                log.error("获取白丝图片失败: {}", result.error)
            }

        } catch (Exception e) {
            log.error("处理白丝图片请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 发送图片消息
     */
    private void sendImageMessage(QQEvent event, String imageUrl) {
        try {
            // 创建图片消息格式
            List imageMessage = [
                [
                    type: "image",
                    data: [
                        file: imageUrl
                    ]
                ]
            ]

            // 支持私聊和群聊发送图片
            if (event.isPrivateMessage()) {
                apiService.sendPrivateMessageWithFormat(event.getUserIdStr(), imageMessage)
            } else if (event.isGroupMessage()) {
                apiService.sendGroupMessageWithFormat(event.getGroupIdStr(), imageMessage)
            }

            log.info("图片消息发送成功: imageUrl={}", imageUrl)
        } catch (Exception e) {
            log.error("发送图片消息失败: imageUrl={}", imageUrl, e)
            sendReply(event, "❌ 图片发送失败，请稍后再试")
        }
    }

    /**
     * 发送文本回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
