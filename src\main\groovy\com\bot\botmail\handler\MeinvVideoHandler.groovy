package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.MeinvVideoApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture

/**
 * 美女视频 事件处理器
 */
@Component
@Slf4j
class MeinvVideoHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private MeinvVideoApiService meinvVideoService

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        
        // 处理命令：给爷舞个
        return messageText == "给爷舞个"
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()
        
        log.info("收到美女视频请求: userId={}, command={}", userId, messageText)

        // 异步处理，避免阻塞
        CompletableFuture.runAsync {
            handleMeinvVideoRequest(event)
        }
    }

    @Override
    int getPriority() {
        return 9 // 高优先级
    }

    /**
     * 处理美女视频请求
     */
    private void handleMeinvVideoRequest(QQEvent event) {
        try {
            sendReply(event, "🎬 正在为你获取随机美女视频...")

            // 调用API获取视频URL
            Map result = meinvVideoService.getRandomMeinvVideo()

            if (result.success) {
                String videoUrl = result.videoUrl

                // 发送视频消息
                sendVideoMessage(event, videoUrl)

                log.info("美女视频发送成功: userId={}, videoUrl={}", event.getUserIdStr(), videoUrl)
            } else {
                sendReply(event, "❌ 获取美女视频失败: ${result.error}")
                log.error("获取美女视频失败: {}", result.error)
            }

        } catch (Exception e) {
            log.error("处理美女视频请求失败", e)
            sendReply(event, "❌ 处理请求时出现错误，请稍后再试")
        }
    }

    /**
     * 发送视频消息
     */
    private void sendVideoMessage(QQEvent event, String videoUrl) {
        try {
            // 创建视频消息格式
            List videoMessage = [
                [
                    type: "video",
                    data: [
                        file: videoUrl
                    ]
                ]
            ]

            // 支持私聊和群聊发送视频
            if (event.isPrivateMessage()) {
                apiService.sendPrivateMessageWithFormat(event.getUserIdStr(), videoMessage)
            } else if (event.isGroupMessage()) {
                apiService.sendGroupMessageWithFormat(event.getGroupIdStr(), videoMessage)
            }

            log.info("视频消息发送成功: videoUrl={}", videoUrl)
        } catch (Exception e) {
            log.error("发送视频消息失败: videoUrl={}", videoUrl, e)
            sendReply(event, "❌ 视频发送失败，请稍后再试")
        }
    }

    /**
     * 发送文本回复消息
     */
    private void sendReply(QQEvent event, String message) {
        if (event.isPrivateMessage()) {
            apiService.sendPrivateMessage(event.getUserIdStr(), message)
        } else if (event.isGroupMessage()) {
            apiService.sendGroupMessage(event.getGroupIdStr(), message)
        }
    }
}
