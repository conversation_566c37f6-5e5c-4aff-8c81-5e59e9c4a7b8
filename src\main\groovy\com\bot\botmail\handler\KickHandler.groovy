package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

/**
 * 踢人功能处理器
 */
@Component
@Slf4j
class KickHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Value('${bot.admin-qq}')
    private String adminQq

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        return event.message instanceof List && hasAtAndKick(event.message)
    }

    @Override
    void handle(QQEvent event) {
        String messageText = event.getMessageText()
        String userId = event.getUserIdStr()
        
        log.info("收到踢人请求: userId={}, message={}", userId, messageText)

        handleKickCommand(event, messageText)
    }

    @Override
    int getPriority() {
        return 8 // 高优先级
    }

    /**
     * 检查消息是否包含@用户和"踢"字（踢人格式）
     */
    private boolean hasAtAndKick(Object message) {
        if (!(message instanceof List)) {
            return false
        }

        boolean hasAt = false
        boolean hasKick = false

        message.each { segment ->
            if (segment instanceof Map) {
                if (segment.type == "at") {
                    hasAt = true
                } else if (segment.type == "text" && segment.data?.text?.contains("踢")) {
                    hasKick = true
                }
            }
        }

        return hasAt && hasKick
    }

    /**
     * 处理踢人命令
     */
    private void handleKickCommand(QQEvent event, String messageText) {
        // 只在群聊中处理踢人命令
        if (!event.isGroupMessage()) {
            return
        }

        // 检查是否为管理员
        if (!isAdmin(event)) {
            sendReply(event, "❌ 只有管理员可以使用踢人功能")
            return
        }

        try {
            // 从消息中提取被@的用户ID
            String targetUserId = null
            if (event.message instanceof List) {
                for (segment in event.message) {
                    if (segment instanceof Map && segment.type == "at") {
                        targetUserId = segment.data?.qq?.toString()
                        break
                    }
                }
            }

            if (!targetUserId) {
                sendReply(event, "❌ 请@要踢出的用户")
                return
            }

            String groupId = event.getGroupIdStr()

            log.info("管理员 {} 在群 {} 踢出用户 {}",
                    event.getUserIdStr(), groupId, targetUserId)

            // 调用踢人 API
            def result = apiService.kickGroupMember(groupId, targetUserId, false)

            if (result.isSuccess()) {
                // 发送踢人成功消息
                List kickSuccessMessage = [
                    [type: "text", data: [text: "👢 踢人成功！\n━━━━━━━━━━━━━━━\n👤 用户："]],
                    [type: "at", data: [qq: Long.parseLong(targetUserId)]],
                    [type: "text", data: [text: "\n👮 执行者："]],
                    [type: "at", data: [qq: Long.parseLong(event.getUserIdStr())]],
                    [type: "text", data: [text: "\n🚪 已被踢出群聊\n━━━━━━━━━━━━━━━"]]
                ]
                apiService.sendGroupMessageWithFormat(groupId, kickSuccessMessage)
            } else {
                sendReply(event, "❌ 踢人失败：${result.getErrorMessage()}")
            }

        } catch (Exception e) {
            log.error("处理踢人命令失败", e)
            sendReply(event, "❌ 踢人命令处理失败：${e.message}")
        }
    }

    /**
     * 检查是否为管理员
     */
    private boolean isAdmin(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
