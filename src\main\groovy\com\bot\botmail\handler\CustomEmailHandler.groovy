package com.bot.botmail.handler

import com.bot.botmail.entity.QQEvent
import com.bot.botmail.service.LLOneBotApiService
import com.bot.botmail.service.GoMailApiService
import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

import java.util.concurrent.CompletableFuture
import java.util.regex.Pattern

/**
 * 自定义邮箱处理器（仅限机器人主人）
 */
@Component
@Slf4j
class CustomEmailHandler implements EventHandler {

    @Autowired
    private LLOneBotApiService apiService

    @Autowired
    private GoMailApiService goMailService

    @Value('${bot.admin-qq}')
    private String adminQq

    private Random random = new Random()

    @Override
    boolean canHandle(QQEvent event) {
        if (!event.isMessageEvent()) {
            return false
        }

        String messageText = event.getMessageText().trim()
        return messageText.startsWith("创建邮箱 ")
    }

    @Override
    void handle(QQEvent event) {
        String userId = event.getUserIdStr()
        String messageText = event.getMessageText().trim()
        
        // 检查是否为机器人主人
        if (!isOwner(event)) {
            sendReply(event, "❌ 此功能仅限机器人主人使用")
            return
        }

        log.info("收到自定义邮箱请求: userId={}, message={}", userId, messageText)

        // 解析命令格式：创建邮箱 前缀@域名
        String emailPart = messageText.substring(5).trim() // 去掉"创建邮箱 "
        
        if (emailPart.isEmpty()) {
            sendReply(event, """❌ 邮箱格式不能为空
━━━━━━━━━━━━━━━
💡 正确格式：创建邮箱 前缀@域名
📝 例如：创建邮箱 <EMAIL>
📝 例如：创建邮箱 <EMAIL>
━━━━━━━━━━━━━━━""")
            return
        }

        // 验证邮箱格式
        if (!isValidEmailFormat(emailPart)) {
            sendReply(event, """❌ 邮箱格式不正确
━━━━━━━━━━━━━━━
💡 正确格式：前缀@域名
📝 例如：<EMAIL>
📝 例如：<EMAIL>
━━━━━━━━━━━━━━━""")
            return
        }

        sendReply(event, "🔄 正在创建自定义邮箱，请稍候...")

        CompletableFuture.runAsync {
            try {
                // 解析邮箱地址
                String[] parts = emailPart.split("@")
                String prefix = parts[0]
                String domain = parts[1]

                // 创建自定义邮箱 - 使用GoMail自定义接口
                Map mailboxResult = goMailService.createCustomMailbox(prefix, domain)
                if (!mailboxResult.success) {
                    sendReply(event, "❌ 创建自定义邮箱失败: ${mailboxResult.error}")
                    return
                }

                String actualEmail = mailboxResult.data.email

                sendReply(event, """📧 自定义邮箱创建成功！
━━━━━━━━━━━━━━━
📮 邮箱地址: ${actualEmail}
🏷️ 类型: 主人专属自定义邮箱
⏰ 有效期: 24小时
👑 权限: 无限制使用
🔍 开始监控邮件... (60秒内持续检查)
━━━━━━━━━━━━━━━""")

                // 监控邮件（60秒）
                long startTime = System.currentTimeMillis()
                long timeout = 60000 // 60秒
                boolean found = false
                int checkCount = 0

                while (System.currentTimeMillis() - startTime < timeout && !found) {
                    checkCount++

                    // 每3秒检查一次
                    if (checkCount > 1) {
                        Thread.sleep(3000)
                    }

                    log.info("第 {} 次检查自定义邮箱: {}", checkCount, actualEmail)

                    // 获取邮件列表
                    Map emailsResult = goMailService.getEmails(actualEmail)
                    if (!emailsResult.success) {
                        log.warn("获取自定义邮件失败: {}", emailsResult.error)
                        continue
                    }

                    List emails = emailsResult.data?.emails
                    if (!emails || emails.isEmpty()) {
                        log.info("第 {} 次检查: 暂无自定义邮件", checkCount)
                        continue
                    }

                    // 获取第一封邮件详情
                    String firstEmailId = emails[0].id
                    Map emailDetail = goMailService.getEmailDetail(firstEmailId)

                    if (!emailDetail.success) {
                        log.warn("获取自定义邮件详情失败: {}", emailDetail.error)
                        continue
                    }

                    // 提取验证码
                    String subject = emailDetail.data?.subject ?: ""
                    String textContent = emailDetail.data?.textContent ?: ""
                    String htmlContent = emailDetail.data?.htmlContent ?: ""

                    String verificationCode = extractVerificationCode(subject, textContent, htmlContent)

                    if (verificationCode) {
                        found = true
                        long elapsedTime = (System.currentTimeMillis() - startTime) / 1000
                        
                        sendReply(event, """✅ 自定义邮箱验证码获取成功！
━━━━━━━━━━━━━━━
📧 邮箱: ${actualEmail}
📨 主题: ${subject}
🔑 验证码: ${verificationCode}
⏱️ 用时: ${elapsedTime}秒
👑 主人专属
━━━━━━━━━━━━━━━""")
                    } else {
                        log.info("第 {} 次检查: 暂无邮件", checkCount)
                    }
                }

                // 如果60秒内没有找到验证码
                if (!found) {
                    sendReply(event, """⏰ 自定义邮箱监控超时 (60秒)
━━━━━━━━━━━━━━━
📧 邮箱: ${actualEmail}
📭 结果: 未收到包含验证码的邮件
💡 建议: 重新创建邮箱或稍后再试
👑 主人专属
━━━━━━━━━━━━━━━""")
                }

            } catch (Exception e) {
                log.error("处理自定义邮箱请求失败", e)
                sendReply(event, "❌ 自定义邮箱功能暂时不可用，请稍后再试")
            }
        }
    }

    @Override
    int getPriority() {
        return 9 // 最高优先级
    }

    /**
     * 验证邮箱格式
     */
    private boolean isValidEmailFormat(String email) {
        // 基本的邮箱格式验证
        Pattern emailPattern = Pattern.compile(/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/)
        return emailPattern.matcher(email).matches()
    }



    /**
     * 提取验证码
     */
    private String extractVerificationCode(String subject, String textContent, String htmlContent) {
        // 常见验证码模式
        List<Pattern> patterns = [
            Pattern.compile("验证码[：:]?\\s*([0-9]{4,8})"),
            Pattern.compile("verification code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("code[：:]?\\s*([0-9]{4,8})", Pattern.CASE_INSENSITIVE),
            Pattern.compile("([0-9]{6})"),
            Pattern.compile("([0-9]{4})")
        ]

        String allContent = "${subject} ${textContent} ${htmlContent}"

        for (Pattern pattern : patterns) {
            def matcher = pattern.matcher(allContent)
            if (matcher.find()) {
                return matcher.group(1)
            }
        }

        return null
    }

    /**
     * 检查是否为机器人主人
     */
    private boolean isOwner(QQEvent event) {
        return adminQq && event.getUserIdStr() == adminQq
    }

    /**
     * 发送回复消息
     */
    private void sendReply(QQEvent event, String message) {
        try {
            if (event.isGroupMessage()) {
                apiService.sendGroupMessage(event.getGroupIdStr(), message)
            } else {
                apiService.sendPrivateMessage(event.getUserIdStr(), message)
            }
        } catch (Exception e) {
            log.error("发送回复消息失败", e)
        }
    }
}
