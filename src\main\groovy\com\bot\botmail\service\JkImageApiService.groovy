package com.bot.botmail.service

import groovy.util.logging.Slf4j
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

/**
 * JK图片 API 服务
 */
@Service
@Slf4j
class JkImageApiService {

    @Autowired
    private XxApiService xxApiService

    /**
     * 获取随机JK图片
     */
    Map getRandomJkImage() {
        Map result = xxApiService.getJkImage()
        if (result.success) {
            return [
                success: true,
                imageUrl: result.data,
                message: result.message
            ]
        } else {
            return result
        }
    }
}
